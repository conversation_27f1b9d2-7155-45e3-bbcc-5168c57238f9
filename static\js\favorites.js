/**
 * 收藏页面脚本
 * 处理收藏列表的加载、搜索、过滤和分页
 */

class FavoritesPage {
    constructor() {
        this.favorites = [];
        this.totalCount = 0;
        this.currentPage = 1;
        this.pageSize = 24;
        this.isLoading = false;
        this.searchTimeout = null;

        // 排序参数
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';

        // 过滤参数
        this.filters = {
            search: ''
        };

        // 批量工具栏
        this.batchToolbar = null;
    }

    /**
     * 初始化收藏页面
     */
    async init() {
        this.bindEvents();
        this.initBatchToolbar();
        await this.loadFavorites();
        this.initializeFromUrl();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 搜索相关事件
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');
        const clearSearchInputBtn = document.getElementById('clear-search-input-btn');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });
        }

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.handleSearch();
            });
        }

        if (clearSearchInputBtn) {
            clearSearchInputBtn.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // 排序选择事件
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.handleSortChange(e.target.value);
            });
        }



        // 刷新和清除搜索按钮
        const refreshBtn = document.getElementById('refresh-btn');
        const clearSearchBtn = document.getElementById('clear-search-btn');

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadFavorites();
            });
        }

        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // 错误重试按钮
        const errorRetryBtn = document.getElementById('error-retry-btn');
        if (errorRetryBtn) {
            errorRetryBtn.addEventListener('click', () => {
                this.loadFavorites();
            });
        }

        // 影片卡片点击事件
        document.addEventListener('click', (e) => {
            // 检查是否点击了按钮、链接、复选框或其他交互元素，如果是则不处理卡片点击
            if (e.target.closest('button') ||
                e.target.closest('a') ||
                e.target.closest('.btn') ||
                e.target.closest('input[type="checkbox"]') ||
                e.target.closest('.batch-select-container') ||
                e.target.closest('.favorite-btn-container')) {
                return;
            }

            const movieCard = e.target.closest('.movie-card');
            if (movieCard) {
                const movieId = movieCard.getAttribute('data-movie-id');
                if (movieId) {
                    this.showMovieDetail(movieId);
                }
            }
        });

        // 批量选择相关事件
        const batchSelectBtn = document.getElementById('batch-select-btn');
        if (batchSelectBtn) {
            batchSelectBtn.addEventListener('click', () => {
                this.toggleBatchMode();
            });
        }
    }

    /**
     * 初始化批量工具栏
     */
    initBatchToolbar() {
        if (window.BatchToolbar) {
            this.batchToolbar = new BatchToolbar('favorites-container');
        }
    }

    /**
     * 切换批量模式
     */
    toggleBatchMode() {
        if (!window.favoriteManager) return;

        const isCurrentlyBatchMode = window.favoriteManager.isBatchMode();
        const newBatchMode = !isCurrentlyBatchMode;

        // 切换批量模式
        window.favoriteManager.toggleBatchMode(newBatchMode);

        // 更新批量选择按钮状态
        this.updateBatchSelectButton(newBatchMode);
    }

    /**
     * 更新批量选择按钮状态
     * @param {boolean} batchMode - 是否批量模式
     */
    updateBatchSelectButton(batchMode) {
        const batchSelectBtn = document.getElementById('batch-select-btn');
        if (!batchSelectBtn) return;

        if (batchMode) {
            batchSelectBtn.classList.remove('btn-outline');
            batchSelectBtn.classList.add('btn-primary');
            batchSelectBtn.innerHTML = `
                <i class="bi bi-check-square-fill" aria-label="批量选择图标"></i>
                退出批量
            `;
        } else {
            batchSelectBtn.classList.remove('btn-primary');
            batchSelectBtn.classList.add('btn-outline');
            batchSelectBtn.innerHTML = `
                <i class="bi bi-check-square" aria-label="批量选择图标"></i>
                批量选择
            `;
        }
    }

    async loadFavorites() {
        if (this.isLoading) return;

        try {
            this.isLoading = true;
            this.showLoading();

            // 构建过滤参数
            const filterParams = {
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.filters
            };

            // 清理空值
            Object.keys(filterParams).forEach(key => {
                if (filterParams[key] === '' || filterParams[key] === null || 
                    (Array.isArray(filterParams[key]) && filterParams[key].length === 0)) {
                    delete filterParams[key];
                }
            });

            const response = await api.getFavorites(filterParams);

            if (response.success) {
                this.favorites = response.data || [];
                this.totalCount = response.total_count || 0;

                this.updateFavoritesDisplay();
                this.updatePagination();
                this.updateUrl();

                // 如果在批量模式下，更新UI
                if (window.favoriteManager && window.favoriteManager.isBatchMode()) {
                    this.updateBatchSelectButton(true);
                    window.favoriteManager.updateMovieCardsSelection();
                }

            } else {
                throw new Error(response.message || '获取收藏列表失败');
            }

        } catch (error) {
            console.error('加载收藏列表失败:', error);
            // 使用Toast显示错误，同时禁用handleApiError的自动Toast以避免重复
            const errorMessage = handleApiError(error, '加载收藏列表', false);
            toast.error(errorMessage);
            this.showErrorState(errorMessage);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    

    /**
     * 更新收藏显示
     */
    updateFavoritesDisplay() {
        const container = document.getElementById('favorites-container');
        if (!container) return;

        if (this.favorites.length === 0) {
            this.showEmptyState();
            return;
        }

        const favoritesHtml = this.favorites.map(favorite => this.createFavoriteCard(favorite)).join('');
        container.innerHTML = favoritesHtml;

        // 启用图片懒加载
        utils.lazyLoadImages(container);

        // 添加淡入动画
        container.classList.add('fade-in');

        // 异步更新收藏状态（所有都应该是已收藏状态）
        this.updateFavoritesDisplayWithFavorites();

        // 如果在批量模式下，确保复选框可见
        if (window.favoriteManager && window.favoriteManager.isBatchMode()) {
            const checkboxContainers = container.querySelectorAll('.batch-select-container');
            checkboxContainers.forEach(c => c.classList.remove('hidden'));
            window.favoriteManager.updateMovieCardsSelection();
        }
    }

    /**
     * 创建收藏卡片HTML
     */
    createFavoriteCard(favorite) {
        const movie = favorite.movie;
        if (!movie) return '';

        // 构建图片URL
        let imageUrl = '';
        if (movie.poster_uuid) {
            imageUrl = `/api/images/${movie.poster_uuid}`;
        } else if (movie.fanart_uuid) {
            imageUrl = `/api/images/${movie.fanart_uuid}`;
        } else if (movie.thumb_uuid) {
            imageUrl = `/api/images/${movie.thumb_uuid}`;
        }

        // 格式化评分
        const rating = movie.rating ? parseFloat(movie.rating).toFixed(1) : '';

        // 格式化时长
        const runtime = movie.runtime ? `${movie.runtime}分钟` : '';

        // 格式化收藏时间
        const favoriteDate = new Date(favorite.created_at).toLocaleDateString('zh-CN');

        return `
            <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group movie-card" data-movie-id="${movie.id}">
                <figure class="relative aspect-[2/3] overflow-hidden">
                    ${imageUrl ?
                        `<img src="${imageUrl}"
                             alt="${movie.title}"
                             class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                             loading="lazy"
                             onerror="this.onerror=null; this.classList.add('hidden'); this.nextElementSibling.classList.remove('hidden'); this.nextElementSibling.classList.add('flex');">
                         <div class="absolute inset-0 flex items-center justify-center bg-base-200 text-base-content/50 hidden">
                            <i class="bi bi-film" style="font-size: 3rem;" aria-label="默认海报图标"></i>
                        </div>` :
                        `<div class="absolute inset-0 flex items-center justify-center bg-base-200 text-base-content/50">
                            <i class="bi bi-film" style="font-size: 3rem;" aria-label="默认海报图标"></i>
                        </div>`
                    }

                    <!-- 批量选择复选框 -->
                    <div class="batch-select-container absolute top-2 left-2 hidden">
                        <input type="checkbox" class="checkbox checkbox-primary batch-select-checkbox" data-movie-id="${movie.id}">
                    </div>

                    <!-- 收藏状态指示器（始终可见，在批量模式下调整位置） -->
                    <div class="absolute top-2 right-2 z-10 favorite-status-indicator transition-all duration-300"
                         data-movie-id="${movie.id}"
                         style="transform: translateX(0);">
                        <!-- 收藏状态图标将在这里动态生成 -->
                    </div>

                    <!-- 收藏按钮容器（悬停时显示） -->
                    <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20 favorite-btn-container" data-movie-id="${movie.id}">
                        <!-- 收藏按钮将在这里动态生成 -->
                    </div>

                    <!-- 评分标签 -->
                    ${rating ? `
                        <div class="absolute bottom-2 left-2">
                            <div class="badge badge-primary badge-sm">
                                <i class="bi bi-star-fill mr-1" aria-label="评分图标"></i>
                                ${rating}
                            </div>
                        </div>
                    ` : ''}
                </figure>

                <div class="card-body p-2">
                    <h3 class="text-xs font-medium line-clamp-2" title="${this.escapeHtml(movie.title)}">
                        ${this.escapeHtml(movie.title)}
                    </h3>

                    <div class="flex flex-col gap-1 text-xs text-base-content/70">
                        ${movie.year ? `
                            <div class="flex items-center gap-1">
                                <i class="bi bi-collection" aria-label="系列图标"></i>
                                <span class="truncate">${movie.year}</span>
                            </div>
                        ` : ''}

                        ${runtime ? `
                            <div class="flex items-center gap-1">
                                <i class="bi bi-clock" aria-label="时长图标"></i>
                                <span>${runtime}</span>
                            </div>
                        ` : ''}

                        <div class="flex items-center gap-1">
                            <i class="bi bi-heart-fill text-primary" aria-label="收藏时间图标"></i>
                            <span>${favoriteDate}</span>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="flex justify-between items-center mt-2 text-xs text-base-content/60">
                        <div class="flex items-center gap-2">
                            ${movie.genre_count > 0 ? `
                                <span class="flex items-center gap-1">
                                    <i class="bi bi-tags" aria-label="分类图标"></i>
                                    ${movie.genre_count}
                                </span>
                            ` : ''}
                            ${movie.tag_count > 0 ? `
                                <span class="flex items-center gap-1">
                                    <i class="bi bi-bookmark" aria-label="标签图标"></i>
                                    ${movie.tag_count}
                                </span>
                            ` : ''}
                            ${movie.actor_count > 0 ? `
                                <span class="flex items-center gap-1">
                                    <i class="bi bi-people" aria-label="演员图标"></i>
                                    ${movie.actor_count}
                                </span>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 异步更新收藏显示状态
     */
    async updateFavoritesDisplayWithFavorites() {
        // 收藏页面中的所有影片都应该是已收藏状态
        // 使用 favoriteManager 动态生成收藏按钮和状态指示器，确保与影片库页面样式一致
        const container = document.getElementById('favorites-container');
        if (!container || !window.favoriteManager) return;

        const movieIds = this.favorites.map(favorite => favorite.movie.id);

        // 为每个影片创建收藏按钮和状态指示器
        movieIds.forEach(movieId => {
            // 创建收藏按钮（悬停时显示）
            const btnContainer = container.querySelector(`.favorite-btn-container[data-movie-id="${movieId}"]`);
            if (btnContainer) {
                const buttonHtml = window.favoriteManager.createFavoriteButton(
                    movieId,
                    true, // 已收藏
                    'sm'  // 小尺寸
                );
                btnContainer.innerHTML = buttonHtml;
            }

            // 创建收藏状态指示器（始终可见）
            const statusIndicator = container.querySelector(`.favorite-status-indicator[data-movie-id="${movieId}"]`);
            if (statusIndicator) {
                const indicatorHtml = window.favoriteManager.createFavoriteStatusIndicator(
                    movieId,
                    true, // 已收藏
                    'sm'  // 小尺寸
                );
                statusIndicator.innerHTML = indicatorHtml;
                statusIndicator.classList.remove('hidden');
            }
        });
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const container = document.getElementById('favorites-container');
        const skeleton = document.getElementById('loading-skeleton');
        const emptyState = document.getElementById('empty-state');
        const errorState = document.getElementById('error-state');

        // 清空容器内容，避免显示旧数据
        if (container) {
            container.innerHTML = '';
            container.classList.add('hidden');
        }
        if (emptyState) emptyState.classList.add('hidden');
        if (errorState) errorState.classList.add('hidden');

        if (skeleton) {
            skeleton.classList.remove('hidden');
            // 生成骨架屏卡片
            if (skeleton.children.length === 0) {
                const skeletonCards = Array(this.pageSize).fill(0).map(() => `
                    <div class="card bg-base-100 shadow-lg">
                        <figure class="aspect-[2/3] bg-base-300 animate-pulse"></figure>
                        <div class="card-body p-3">
                            <div class="h-4 bg-base-300 rounded animate-pulse mb-2"></div>
                            <div class="h-3 bg-base-300 rounded animate-pulse mb-1"></div>
                            <div class="h-3 bg-base-300 rounded animate-pulse w-2/3"></div>
                        </div>
                    </div>
                `).join('');
                skeleton.innerHTML = skeletonCards;
            }
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const container = document.getElementById('favorites-container');
        const skeleton = document.getElementById('loading-skeleton');

        if (skeleton) skeleton.classList.add('hidden');
        if (container) container.classList.remove('hidden');
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        const container = document.getElementById('favorites-container');
        const emptyState = document.getElementById('empty-state');
        const errorState = document.getElementById('error-state');

        if (container) container.classList.add('hidden');
        if (errorState) errorState.classList.add('hidden');
        if (emptyState) emptyState.classList.remove('hidden');
    }

    /**
     * 显示错误状态
     */
    showErrorState(message) {
        const container = document.getElementById('favorites-container');
        const emptyState = document.getElementById('empty-state');
        const errorState = document.getElementById('error-state');
        const errorMessage = document.getElementById('error-message');

        if (container) container.classList.add('hidden');
        if (emptyState) emptyState.classList.add('hidden');
        if (errorState) errorState.classList.remove('hidden');
        if (errorMessage) errorMessage.textContent = message;
    }

    /**
     * 处理搜索输入
     */
    handleSearchInput(value) {
        // 显示/隐藏清除按钮
        const clearBtn = document.getElementById('clear-search-input-btn');
        if (clearBtn) {
            if (value.trim()) {
                clearBtn.classList.remove('hidden');
            } else {
                clearBtn.classList.add('hidden');
            }
        }

        // 防抖搜索
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        this.searchTimeout = setTimeout(() => {
            this.filters.search = value.trim();
            this.currentPage = 1;
            this.loadFavorites();
        }, 500);
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            this.filters.search = searchInput.value.trim();
            this.currentPage = 1;
            this.loadFavorites();
        }
    }

    /**
     * 清除搜索
     */
    clearSearch() {
        const searchInput = document.getElementById('search-input');
        const clearBtn = document.getElementById('clear-search-input-btn');

        if (searchInput) {
            searchInput.value = '';
        }
        if (clearBtn) {
            clearBtn.classList.add('hidden');
        }

        this.filters.search = '';
        this.currentPage = 1;
        this.loadFavorites();
    }

    /**
     * 处理排序变更
     */
    handleSortChange(value) {
        const [sortBy, sortOrder] = value.split('-');
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
        this.currentPage = 1;
        this.loadFavorites();
    }



    /**
     * 更新分页
     */
    updatePagination() {
        const totalPages = Math.ceil(this.totalCount / this.pageSize);
        const startIndex = (this.currentPage - 1) * this.pageSize + 1;
        const endIndex = Math.min(this.currentPage * this.pageSize, this.totalCount);

        // 更新分页信息
        const paginationInfo = document.getElementById('pagination-info');
        if (paginationInfo) {
            paginationInfo.textContent = `第 ${this.currentPage} 页，共 ${totalPages} 页 (显示 ${startIndex} - ${endIndex} 条，共 ${this.totalCount} 条记录)`;
        }

        // 更新分页控件
        const paginationControls = document.getElementById('pagination-controls');
        if (paginationControls && totalPages > 1) {
            paginationControls.innerHTML = this.generatePaginationHTML(totalPages);
            this.bindPaginationEvents();
        } else if (paginationControls) {
            paginationControls.innerHTML = '';
        }
    }

    /**
     * 生成分页HTML
     */
    generatePaginationHTML(totalPages) {
        let html = '';

        // 上一页按钮
        html += `
            <button class="join-item btn ${this.currentPage === 1 ? 'btn-disabled' : ''}"
                    data-page="${this.currentPage - 1}"
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="bi bi-chevron-left" aria-label="上一页图标"></i>
                上一页
            </button>
        `;

        // 下一页按钮
        html += `
            <button class="join-item btn ${this.currentPage === totalPages ? 'btn-disabled' : ''}"
                    data-page="${this.currentPage + 1}"
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                下一页
                <i class="bi bi-chevron-right" aria-label="下一页图标"></i>
            </button>
        `;

        return html;
    }

    /**
     * 绑定分页事件
     */
    bindPaginationEvents() {
        const paginationButtons = document.querySelectorAll('#pagination-controls button[data-page]');
        paginationButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const page = parseInt(e.target.closest('button').dataset.page);
                if (page && page !== this.currentPage && !e.target.closest('button').disabled) {
                    this.currentPage = page;
                    this.loadFavorites();
                }
            });
        });
    }

    /**
     * 显示影片详情
     */
    showMovieDetail(movieId) {
        if (movieId) {
            window.location.href = `/movies/info/${movieId}`;
        }
    }

    /**
     * 切换批量模式
     */
    toggleBatchMode() {
        if (!window.favoriteManager) return;

        const isCurrentlyBatchMode = window.favoriteManager.isBatchMode();
        const newBatchMode = !isCurrentlyBatchMode;

        // 切换批量模式
        window.favoriteManager.toggleBatchMode(newBatchMode);

        // 更新批量选择按钮状态
        this.updateBatchSelectButton(newBatchMode);
    }

    /**
     * 从URL初始化参数
     */
    initializeFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);

        // 从URL获取搜索参数
        const search = urlParams.get('search');
        if (search) {
            this.filters.search = search;
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.value = search;
            }
        }

        // 从URL获取页码
        const page = urlParams.get('page');
        if (page && !isNaN(page)) {
            this.currentPage = Math.max(1, parseInt(page));
        }

        // 从URL获取排序参数
        const sort = urlParams.get('sort');
        if (sort) {
            const sortSelect = document.getElementById('sort-select');
            if (sortSelect) {
                sortSelect.value = sort;
                this.handleSortChange(sort);
            }
        }
    }

    /**
     * 更新URL参数
     */
    updateUrl() {
        const url = new URL(window.location);

        // 更新搜索参数
        if (this.filters.search) {
            url.searchParams.set('search', this.filters.search);
        } else {
            url.searchParams.delete('search');
        }

        // 更新页码参数
        if (this.currentPage > 1) {
            url.searchParams.set('page', this.currentPage);
        } else {
            url.searchParams.delete('page');
        }

        // 更新排序参数
        const sortValue = `${this.sortBy}-${this.sortOrder}`;
        if (sortValue !== 'created_at-desc') {
            url.searchParams.set('sort', sortValue);
        } else {
            url.searchParams.delete('sort');
        }

        // 更新浏览器历史记录
        window.history.replaceState({}, '', url);
    }

    /**
     * 获取当前过滤器状态摘要
     */
    getFilterSummary() {
        const activeFilters = [];

        if (this.filters.search) {
            activeFilters.push(`搜索: ${this.filters.search}`);
        }

        return activeFilters.length > 0 ? activeFilters.join(', ') : '无筛选条件';
    }

    /**
     * 导出收藏列表
     */
    async exportFavorites() {
        try {
            // 获取所有收藏（不分页）
            const response = await api.getFavorites({
                limit: 10000,
                offset: 0,
                ...this.filters
            });

            if (response.success && response.data) {
                const favorites = response.data;
                const csvContent = this.generateCSV(favorites);
                this.downloadCSV(csvContent, 'favorites.csv');

                if (window.toast) {
                    window.toast.success(`成功导出 ${favorites.length} 个收藏`);
                }
            } else {
                throw new Error('获取收藏数据失败');
            }
        } catch (error) {
            console.error('导出收藏失败:', error);
            handleApiError(error, '导出收藏');
        }
    }

    /**
     * 生成CSV内容
     */
    generateCSV(favorites) {
        const headers = ['标题', '年份', '评分', '时长', '系列', '收藏时间'];
        const rows = favorites.map(favorite => {
            const movie = favorite.movie;
            return [
                movie.title || '',
                movie.year || '',
                movie.rating || '',
                movie.runtime || '',
                movie.series_name || '',
                new Date(favorite.created_at).toLocaleString('zh-CN')
            ];
        });

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        return csvContent;
    }

    /**
     * 下载CSV文件
     */
    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    /**
     * HTML 转义
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 页面加载完成后初始化收藏页面
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname === '/favorites') {
        const favoritesPage = new FavoritesPage();
        favoritesPage.init();

        // 将实例暴露到全局，方便调试
        window.favoritesPage = favoritesPage;
    }
});
