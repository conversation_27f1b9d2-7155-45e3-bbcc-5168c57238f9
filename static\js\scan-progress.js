/**
 * 扫描进度管理组件
 * 提供扫描进度显示、控制和状态管理功能
 */
class ScanProgressManager {
    constructor() {
        this.api = window.api;
        this.activeTasks = new Map(); // 活跃任务映射
        this.progressUpdateInterval = null;
        this.updateIntervalMs = 2000; // 2秒更新一次
        
        this.initializeProgressContainer();
        this.startProgressMonitoring();
    }
    
    /**
     * 初始化进度容器（已移除浮动窗口）
     */
    initializeProgressContainer() {
        // 不再创建浮动进度容器，进度将直接显示在目录卡片中
    }
    
    /**
     * 开始扫描任务
     */
    async startScan(scanFunction, ...args) {
        try {
            const result = await scanFunction(...args);
            
            if (result.success && result.task_id) {
                // 添加到活跃任务
                this.activeTasks.set(result.task_id, {
                    taskId: result.task_id,
                    scanType: result.scan_type,
                    directoriesCount: result.directories_count,
                    startTime: new Date()
                });
                
                // 进度将在目录卡片中显示，不再创建独立进度卡片
                
                // 显示开始提示
                if (window.toast) {
                    window.toast.info(`${result.scan_type}任务已启动`);
                }
                
                return result.task_id;
            } else {
                // 没有任务ID，可能是没有目录需要扫描
                if (window.toast) {
                    window.toast.info(result.message || '扫描完成');
                }
                return null;
            }
        } catch (error) {
            console.error('启动扫描任务时出错:', error);
            if (window.toast) {
                window.toast.error('启动扫描任务失败');
            }
            throw error;
        }
    }
    
    /**
     * 创建进度卡片（已移除，进度直接显示在目录卡片中）
     */
    createProgressCard(taskId, scanType, directoriesCount) {
        // 不再创建独立的进度卡片
        // 进度信息将直接集成到目录卡片中显示
    }
    
    /**
     * 更新进度卡片
     */
    updateProgressCard(taskId, progress) {
        const card = document.getElementById(`progress-card-${taskId}`);
        if (!card) return;
        
        const progressBar = card.querySelector('.progress-bar');
        const statusText = card.querySelector('.status-text');
        const percentageText = card.querySelector('.percentage-text');
        const currentDirectory = card.querySelector('.current-directory');
        const currentFile = card.querySelector('.current-file');
        const filesCount = card.querySelector('.files-count');
        const moviesCount = card.querySelector('.movies-count');
        const elapsedTime = card.querySelector('.elapsed-time');
        const remainingTime = card.querySelector('.remaining-time');
        const pauseBtn = card.querySelector('.pause-btn');
        const resumeBtn = card.querySelector('.resume-btn');
        
        // 更新进度条 - 前端计算进度百分比
        let percentage = 0;
        if (progress.total_files > 0) {
            percentage = Math.round((progress.processed_files / progress.total_files) * 100);
        }

        if (progressBar.tagName === 'PROGRESS') {
            progressBar.value = percentage;
        } else {
            progressBar.style.width = `${percentage}%`;
        }
        percentageText.textContent = `${percentage}%`;
        
        // 更新状态文本
        const statusMap = {
            'idle': '准备中...',
            'running': '扫描中...',
            'paused': '已暂停',
            'completed': '已完成',
            'cancelled': '已取消',
            'error': '发生错误'
        };
        statusText.textContent = statusMap[progress.status] || progress.status;
        
        // 更新当前目录和文件
        if (progress.current_directory_name) {
            currentDirectory.textContent = `目录: ${progress.current_directory_name}`;
        }
        
        if (progress.current_file_path) {
            const fileName = progress.current_file_path.split(/[/\\]/).pop();
            currentFile.textContent = `文件: ${fileName}`;
        }
        
        // 更新统计信息
        filesCount.textContent = `文件: ${progress.processed_files || 0}/${progress.total_files || 0}`;

        // 构建影片统计信息
        const newMovies = progress.new_movies || 0;
        const updatedMovies = progress.updated_movies || 0;
        const deletedMovies = progress.deleted_movies || 0;

        let movieStats = [];
        if (newMovies > 0) movieStats.push(`+${newMovies}`);
        if (updatedMovies > 0) movieStats.push(`~${updatedMovies}`);
        if (deletedMovies > 0) movieStats.push(`-${deletedMovies}`);

        moviesCount.textContent = `影片: ${movieStats.length > 0 ? movieStats.join(' ') : '0'}`;
        
        // 更新时间信息
        if (progress.start_time) {
            const startTime = new Date(progress.start_time);
            const elapsed = Math.round((new Date() - startTime) / 1000);
            elapsedTime.textContent = `已用时: ${this.formatTime(elapsed)}`;

            // 前端计算剩余时间
            if (progress.processed_files > 0 && progress.total_files > 0 && elapsed > 0) {
                const avgTimePerFile = elapsed / progress.processed_files;
                const remainingFiles = progress.total_files - progress.processed_files;
                const estimatedRemaining = Math.round(avgTimePerFile * remainingFiles);
                remainingTime.textContent = `剩余: ${this.formatTime(estimatedRemaining)}`;
            } else {
                remainingTime.textContent = `剩余: 计算中...`;
            }
        }
        
        // 更新控制按钮
        if (progress.status === 'paused') {
            pauseBtn.parentElement.classList.add('hidden');
            resumeBtn.parentElement.classList.remove('hidden');
        } else if (progress.status === 'running') {
            pauseBtn.parentElement.classList.remove('hidden');
            resumeBtn.parentElement.classList.add('hidden');
        } else {
            // 完成、取消或错误状态，隐藏所有控制按钮
            pauseBtn.parentElement.classList.add('hidden');
            resumeBtn.parentElement.classList.add('hidden');
        }
        
        // 如果任务完成，设置自动移除
        if (['completed', 'cancelled', 'error'].includes(progress.status)) {
            setTimeout(() => {
                this.removeProgressCard(taskId);
            }, 5000); // 5秒后自动移除
        }
    }
    
    /**
     * 移除进度卡片
     */
    removeProgressCard(taskId) {
        const card = document.getElementById(`progress-card-${taskId}`);
        if (card) {
            card.remove();
        }
        this.activeTasks.delete(taskId);
    }
    
    /**
     * 暂停任务
     */
    async pauseTask(taskId) {
        try {
            const result = await this.api.pauseScan(taskId);
            if (result.success && window.toast) {
                window.toast.info('扫描任务已暂停');
            }
        } catch (error) {
            console.error('暂停扫描任务时出错:', error);
            if (window.toast) {
                window.toast.error('暂停扫描任务失败');
            }
        }
    }
    
    /**
     * 恢复任务
     */
    async resumeTask(taskId) {
        try {
            const result = await this.api.resumeScan(taskId);
            if (result.success && window.toast) {
                window.toast.info('扫描任务已恢复');
            }
        } catch (error) {
            console.error('恢复扫描任务时出错:', error);
            if (window.toast) {
                window.toast.error('恢复扫描任务失败');
            }
        }
    }
    
    /**
     * 取消任务
     */
    async cancelTask(taskId) {
        try {
            const result = await this.api.cancelScan(taskId);
            if (result.success && window.toast) {
                window.toast.info('扫描任务已取消');
            }
        } catch (error) {
            console.error('取消扫描任务时出错:', error);
            if (window.toast) {
                window.toast.error('取消扫描任务失败');
            }
        }
    }
    
    /**
     * 开始进度监控
     */
    startProgressMonitoring() {
        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
        }
        
        this.progressUpdateInterval = setInterval(async () => {
            await this.updateAllProgress();
        }, this.updateIntervalMs);
    }
    
    /**
     * 停止进度监控
     */
    stopProgressMonitoring() {
        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
            this.progressUpdateInterval = null;
        }
    }
    
    /**
     * 更新所有活跃任务的进度
     */
    async updateAllProgress() {
        if (this.activeTasks.size === 0) return;
        
        for (const taskId of this.activeTasks.keys()) {
            try {
                const result = await this.api.getScanProgress(taskId);
                if (result.success && result.progress) {
                    this.updateProgressCard(taskId, result.progress);
                }
            } catch (error) {
                // 任务可能已经不存在，从活跃列表中移除
                console.warn(`获取任务 ${taskId} 进度失败:`, error);
                this.removeProgressCard(taskId);
            }
        }
    }
    
    /**
     * 格式化时间
     */
    formatTime(seconds) {
        if (seconds < 60) {
            return `${seconds}s`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}m ${remainingSeconds}s`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
    }
    
    /**
     * 清理资源
     */
    destroy() {
        this.stopProgressMonitoring();
        this.activeTasks.clear();
        
        const container = document.getElementById('scan-progress-container');
        if (container) {
            container.remove();
        }
    }
}

// 全局扫描进度管理器实例
window.scanProgressManager = new ScanProgressManager();
