"""
媒体管理器的核心数据模型
"""
from sqlalchemy import Column, Integer, String, Text, Float, Boolean, DateTime, ForeignKey, Table, UniqueConstraint
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


# 关联表定义
movie_tags = Table(
    'movie_tags',
    BaseModel.metadata,
    Column('movie_id', Integer, ForeignKey('movies.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)

movie_genres = Table(
    'movie_genres', 
    BaseModel.metadata,
    Column('movie_id', Integer, ForeignKey('movies.id'), primary_key=True),
    Column('genre_id', Integer, ForeignKey('genres.id'), primary_key=True)
)

movie_actors = Table(
    'movie_actors',
    BaseModel.metadata,
    Column('movie_id', Integer, ForeignKey('movies.id'), primary_key=True),
    Column('actor_id', Integer, ForeignKey('actors.id'), primary_key=True)
)


class Movie(BaseModel):
    """电影主表"""
    __tablename__ = "movies"
    
    # 基本信息
    title = Column(String(255), nullable=False, index=True)
    original_title = Column(String(255), index=True)
    year = Column(Integer, index=True)
    rating = Column(Float)
    runtime = Column(Integer)  # 运行时长（分钟）
    plot = Column(Text)  # 剧情简介
    outline = Column(Text)  # 简短描述
    release_date = Column(DateTime)
    premiered = Column(DateTime)
    country = Column(String(100))
    critic_rating = Column(Float)
    sort_title = Column(String(255))
    trailer = Column(String(500))
    num = Column(String(50))  # 编号
    lock_data = Column(Boolean, default=False)
    date_added = Column(DateTime)
    
    # 文件信息
    file_path = Column(String(500), nullable=False)
    nfo_path = Column(String(500))
    poster_path = Column(String(500))
    fanart_path = Column(String(500))
    thumb_path = Column(String(500))
    
    # 外键关联
    series_id = Column(Integer, ForeignKey('series.id'), nullable=True)
    directory_id = Column(Integer, ForeignKey('directories.id'), nullable=True)

    # 关系定义
    series = relationship("Series", back_populates="movies")
    directory = relationship("Directory", back_populates="movies")
    tags = relationship("Tag", secondary=movie_tags, back_populates="movies")
    genres = relationship("Genre", secondary=movie_genres, back_populates="movies")
    actors = relationship("Actor", secondary=movie_actors, back_populates="movies")


class Tag(BaseModel):
    """标签表"""
    __tablename__ = "tags"
    
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text)
    
    # 关系定义
    movies = relationship("Movie", secondary=movie_tags, back_populates="tags")


class Genre(BaseModel):
    """类型表"""
    __tablename__ = "genres"
    
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text)
    
    # 关系定义
    movies = relationship("Movie", secondary=movie_genres, back_populates="genres")


class Actor(BaseModel):
    """演员表"""
    __tablename__ = "actors"
    
    name = Column(String(255), nullable=False, index=True)
    role = Column(String(255))  # 角色名称
    biography = Column(Text)
    actor_type = Column(String(50), default="Actor")  # 演员类型
    
    # 关系定义
    movies = relationship("Movie", secondary=movie_actors, back_populates="actors")


class Series(BaseModel):
    """系列/合集表"""
    __tablename__ = "series"
    
    name = Column(String(255), nullable=False, unique=True, index=True)
    description = Column(Text)
    
    # 关系定义
    movies = relationship("Movie", back_populates="series")


class Directory(BaseModel):
    """媒体目录配置表"""
    __tablename__ = "directories"

    name = Column(String(255), nullable=False, unique=True)
    enabled = Column(Boolean, default=True)
    last_scan_time = Column(DateTime)
    cover_image_base64 = Column(Text, nullable=True)  # 目录封面图片的base64编码
    cover_generated_at = Column(DateTime, nullable=True)  # 封面生成时间

    # 关系定义
    movies = relationship("Movie", back_populates="directory")
    paths = relationship("DirectoryPath", back_populates="directory", cascade="all, delete-orphan")


class DirectoryPath(BaseModel):
    """目录路径表 - 支持一个目录对应多个路径"""
    __tablename__ = "directory_paths"

    directory_id = Column(Integer, ForeignKey('directories.id', ondelete='CASCADE'), nullable=False)
    path = Column(String(500), nullable=False, unique=True)
    is_primary = Column(Boolean, default=False, nullable=False)  # 是否为主路径
    enabled = Column(Boolean, default=True, nullable=False)

    # 关系定义
    directory = relationship("Directory", back_populates="paths")

    def __repr__(self):
        return f"<DirectoryPath(id={self.id}, directory_id={self.directory_id}, path='{self.path}', is_primary={self.is_primary})>"


class VImage(BaseModel):
    """虚拟图片映射表"""
    __tablename__ = "v_images"
    
    uuid = Column(String(36), nullable=False, unique=True, index=True)
    real_file_path = Column(String(500), nullable=False)
    image_type = Column(String(50), nullable=False)  # poster, fanart, thumb


class Favorite(BaseModel):
    """收藏表"""
    __tablename__ = "favorites"

    movie_id = Column(Integer, ForeignKey('movies.id'), nullable=False, unique=True, index=True)

    # 关系定义
    movie = relationship("Movie", backref="favorite")


class Config(BaseModel):
    """系统配置表"""
    __tablename__ = "configs"

    config_key = Column(String(100), nullable=False, unique=True, index=True)
    config_value = Column(Text)
    description = Column(Text)


class MappingRule(BaseModel):
    """映射规则表"""
    __tablename__ = "mapping_rules"

    type = Column(String(50), nullable=False, index=True)  # tags/genres/actors
    original_value = Column(String(255), nullable=False, index=True)  # 原始值
    mapped_value = Column(String(255), nullable=True)  # 映射值，可为空表示删除
    status = Column(String(20), nullable=False, default='active', index=True)  # active/empty

    # 确保同一类型内原始值不重复
    __table_args__ = (
        UniqueConstraint('type', 'original_value', name='uq_mapping_type_original'),
    )
