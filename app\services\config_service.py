"""
配置管理服务
提供系统配置的读取、写入和管理功能
"""
import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.models.models import Config
from app.schemas.schemas import ConfigCreate, ConfigUpdate, VideoExtensionsConfigRequest
import logging

logger = logging.getLogger(__name__)


class ConfigService:
    """配置管理服务类"""
    
    # 默认视频扩展名
    DEFAULT_VIDEO_EXTENSIONS = ['.mp4', '.mkv']
    
    # 配置键名常量
    CONFIG_KEY_VIDEO_EXTENSIONS = "video_extensions"
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_config(self, config_key: str) -> Optional[Config]:
        """
        获取配置项
        
        Args:
            config_key: 配置键名
            
        Returns:
            配置对象或None
        """
        try:
            return self.db.query(Config).filter(Config.config_key == config_key).first()
        except Exception as e:
            logger.error(f"获取配置项时发生错误: {e}")
            return None
    
    def create_config(self, config_data: ConfigCreate) -> Optional[Config]:
        """
        创建配置项
        
        Args:
            config_data: 配置创建数据
            
        Returns:
            创建的配置对象，如果失败则返回None
        """
        try:
            # 检查配置键是否已存在
            existing_config = self.get_config(config_data.config_key)
            if existing_config:
                logger.warning(f"配置键已存在: {config_data.config_key}")
                return None
            
            config = Config(
                config_key=config_data.config_key,
                config_value=config_data.config_value,
                description=config_data.description
            )
            
            self.db.add(config)
            self.db.commit()
            self.db.refresh(config)
            
            logger.info(f"成功创建配置项: {config.config_key}")
            return config
            
        except IntegrityError:
            self.db.rollback()
            logger.error(f"配置键重复: {config_data.config_key}")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建配置项时发生错误: {e}")
            return None
    
    def update_config(self, config_key: str, config_data: ConfigUpdate) -> Optional[Config]:
        """
        更新配置项
        
        Args:
            config_key: 配置键名
            config_data: 更新数据
            
        Returns:
            更新后的配置对象，如果失败则返回None
        """
        try:
            config = self.get_config(config_key)
            if not config:
                logger.warning(f"配置项不存在: {config_key}")
                return None
            
            # 更新字段
            if config_data.config_value is not None:
                config.config_value = config_data.config_value
            if config_data.description is not None:
                config.description = config_data.description
            
            self.db.commit()
            self.db.refresh(config)
            
            logger.info(f"成功更新配置项: {config_key}")
            return config
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新配置项时发生错误: {e}")
            return None
    
    def set_config(self, config_key: str, config_value: str, description: Optional[str] = None) -> Optional[Config]:
        """
        设置配置项（如果不存在则创建，存在则更新）
        
        Args:
            config_key: 配置键名
            config_value: 配置值
            description: 配置描述
            
        Returns:
            配置对象或None
        """
        try:
            existing_config = self.get_config(config_key)
            
            if existing_config:
                # 更新现有配置
                update_data = ConfigUpdate(config_value=config_value, description=description)
                return self.update_config(config_key, update_data)
            else:
                # 创建新配置
                create_data = ConfigCreate(
                    config_key=config_key,
                    config_value=config_value,
                    description=description
                )
                return self.create_config(create_data)
                
        except Exception as e:
            logger.error(f"设置配置项时发生错误: {e}")
            return None
    
    def get_video_extensions(self) -> List[str]:
        """
        获取视频扩展名配置
        
        Returns:
            视频扩展名列表
        """
        try:
            config = self.get_config(self.CONFIG_KEY_VIDEO_EXTENSIONS)
            
            if config and config.config_value:
                try:
                    # 尝试解析JSON格式的配置值
                    extensions = json.loads(config.config_value)
                    if isinstance(extensions, list) and all(isinstance(ext, str) for ext in extensions):
                        logger.info(f"从数据库获取视频扩展名配置: {extensions}")
                        return extensions
                    else:
                        logger.warning("视频扩展名配置格式无效，使用默认值")
                except json.JSONDecodeError:
                    logger.warning("视频扩展名配置JSON解析失败，使用默认值")
            
            # 如果配置不存在或无效，返回默认值并保存到数据库
            logger.info("使用默认视频扩展名配置")
            self._save_default_video_extensions()
            return self.DEFAULT_VIDEO_EXTENSIONS.copy()
            
        except Exception as e:
            logger.error(f"获取视频扩展名配置时发生错误: {e}")
            return self.DEFAULT_VIDEO_EXTENSIONS.copy()
    
    def set_video_extensions(self, extensions_data: VideoExtensionsConfigRequest) -> bool:
        """
        设置视频扩展名配置
        
        Args:
            extensions_data: 视频扩展名配置数据
            
        Returns:
            是否设置成功
        """
        try:
            # 将扩展名列表转换为JSON字符串
            config_value = json.dumps(extensions_data.extensions)
            
            # 保存到数据库
            config = self.set_config(
                config_key=self.CONFIG_KEY_VIDEO_EXTENSIONS,
                config_value=config_value,
                description="支持的视频文件扩展名列表"
            )
            
            if config:
                logger.info(f"成功设置视频扩展名配置: {extensions_data.extensions}")
                return True
            else:
                logger.error("设置视频扩展名配置失败")
                return False
                
        except Exception as e:
            logger.error(f"设置视频扩展名配置时发生错误: {e}")
            return False
    
    def _save_default_video_extensions(self):
        """保存默认视频扩展名配置到数据库"""
        try:
            config_value = json.dumps(self.DEFAULT_VIDEO_EXTENSIONS)
            self.set_config(
                config_key=self.CONFIG_KEY_VIDEO_EXTENSIONS,
                config_value=config_value,
                description="支持的视频文件扩展名列表（默认配置）"
            )
            logger.info("已保存默认视频扩展名配置到数据库")
        except Exception as e:
            logger.error(f"保存默认视频扩展名配置时发生错误: {e}")
    
    def get_all_configs(self) -> List[Config]:
        """
        获取所有配置项
        
        Returns:
            配置项列表
        """
        try:
            return self.db.query(Config).all()
        except Exception as e:
            logger.error(f"获取所有配置项时发生错误: {e}")
            return []
    
    def delete_config(self, config_key: str) -> bool:
        """
        删除配置项
        
        Args:
            config_key: 配置键名
            
        Returns:
            是否删除成功
        """
        try:
            config = self.get_config(config_key)
            if not config:
                logger.warning(f"配置项不存在: {config_key}")
                return False
            
            self.db.delete(config)
            self.db.commit()
            
            logger.info(f"成功删除配置项: {config_key}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除配置项时发生错误: {e}")
            return False
