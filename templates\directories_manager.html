{% extends "base.html" %}

{% block title %}目录管理 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                    <i class="bi bi-folder text-primary" style="font-size: 2rem;" aria-label="目录管理图标"></i>
                    目录管理
                </h1>
                <p class="text-base-content/70 mt-2">管理媒体文件存储目录</p>
            </div>
            <div class="flex items-center gap-3">
                <button type="button" class="btn btn-primary btn-sm" id="add-directory-btn">
                    <i class="bi bi-plus" aria-label="添加图标"></i>
                    添加目录
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 搜索和操作区域 -->
<div class="card bg-base-100 shadow-lg border border-base-300 mb-4 md:mb-6">
    <div class="card-body p-3 md:p-6">
        <div class="flex flex-col md:flex-row gap-3 md:gap-4 items-stretch md:items-end">
            <!-- 搜索输入框 -->
            <div class="form-control flex-1">
                <label class="label py-1 md:py-2">
                    <span class="label-text font-medium text-sm md:text-base">搜索目录</span>
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="bi bi-search text-base-content/40" aria-label="搜索图标"></i>
                    </div>
                    <input type="text" class="input input-bordered w-full pl-10 input-sm md:input-md" id="search-input" placeholder="搜索目录路径或描述...">
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-control">
                <label class="label py-1 md:py-2 md:opacity-0">
                    <span class="label-text text-sm md:text-base">　</span>
                </label>
                <div class="flex gap-2 flex-wrap">
                    <button type="button" class="btn btn-outline btn-sm md:btn-md flex-1 md:flex-none" id="refresh-directories-btn">
                        <i class="bi bi-arrow-clockwise md:mr-2" aria-label="刷新图标"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-success btn-sm md:btn-md flex-1 md:flex-none hidden" id="batch-scan-btn">
                        <i class="bi bi-check2-all md:mr-2" aria-label="批量扫描图标"></i>
                        批量扫描
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="directories-loading">
    <span class="loading loading-spinner loading-lg mb-4"></span>
    <p class="text-base-content/60">加载目录数据...</p>
</div>

<!-- 空状态 -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="directories-empty-state">
    <div class="bg-base-200 rounded-full p-6 mb-6">
        <i class="bi bi-folder-x text-base-content/40" style="font-size: 4rem;" aria-label="空目录图标"></i>
    </div>
    <h3 class="text-2xl font-bold mb-2 text-base-content">暂无目录</h3>
    <p class="text-base-content/60 mb-6 text-center max-w-md">您还没有添加任何目录，点击下方按钮添加第一个目录。</p>
    <button type="button" class="btn btn-primary btn-wide" id="empty-add-directory-btn">
        <i class="bi bi-plus mr-2" aria-label="添加图标"></i>
        添加第一个目录
    </button>
</div>

<!-- 目录卡片容器 -->
<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4" id="directories-container">
    <!-- 目录卡片将在这里动态加载 -->
</div>

<!-- 添加/编辑目录模态框 -->
<dialog id="directory-modal" class="modal bg-base-100/80 backdrop-blur-sm">
    <div class="modal-box w-11/12 max-w-2xl bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>
        <h3 class="font-bold text-xl mb-6" id="directory-modal-title">添加目录</h3>

        <form id="directory-form" class="space-y-6">
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">目录名称 <span class="text-error">*</span></span>
                </label>
                <input type="text" class="input input-bordered w-full" id="directory-name" name="name" placeholder="输入目录名称" required>
                <div class="label">
                    <span class="label-text-alt text-error hidden" id="directory-name-error"></span>
                    <span class="label-text-alt">请输入目录的显示名称，例如：电影库、电视剧库</span>
                </div>
            </div>

            <!-- 多路径管理区域 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">目录路径 <span class="text-error">*</span></span>
                    <span class="label-text-alt">至少需要一个路径</span>
                </label>

                <!-- 路径列表容器 -->
                <div class="space-y-3" id="directory-paths-container">
                    <!-- 路径项将在这里动态生成 -->
                </div>

                <!-- 添加路径按钮 -->
                <div class="mt-3">
                    <button type="button" class="btn btn-outline btn-sm w-full" id="add-path-btn">
                        <i class="bi bi-plus mr-2" aria-label="添加图标"></i>
                        添加路径
                    </button>
                </div>

                <div class="label">
                    <span class="label-text-alt text-error hidden" id="directory-paths-error"></span>
                    <span class="label-text-alt">可以为同一个目录添加多个路径，系统会自动合并扫描结果</span>
                </div>
            </div>

            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-3">
                    <input type="checkbox" class="checkbox" id="directory-enabled" name="enabled" checked>
                    <span class="label-text">启用此目录</span>
                </label>
            </div>



            <div class="modal-action">
                <button type="button" class="btn btn-outline" onclick="document.getElementById('directory-modal').close()">取消</button>
                <button type="submit" class="btn btn-primary" id="directory-submit-btn">
                    <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                    <i class="bi bi-check mr-2" aria-label="保存图标"></i>
                    保存
                </button>
            </div>
        </form>
    </div>
</dialog>

<!-- 删除确认模态框 -->
<dialog id="delete-directory-modal" class="modal bg-base-100/80 backdrop-blur-sm">
    <div class="modal-box w-11/12 max-w-lg bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">确认删除目录</h3>
        <p class="mb-4" id="delete-directory-message">您确定要删除这个目录吗？</p>

        <div id="delete-directory-warning" class="mb-4 hidden">
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="警告图标"></i>
                <div>
                    <h4 class="font-bold">注意</h4>
                    <div class="text-sm">该目录下有 <strong><span id="directory-movie-count">0</span></strong> 部影片，删除目录将同时删除这些影片的数据记录。</div>
                </div>
            </div>
        </div>

        <div class="alert alert-info mb-6">
            <i class="bi bi-info-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="信息图标"></i>
            <div>
                <h4 class="font-bold">重要说明</h4>
                <div class="text-sm">删除目录只会删除数据库中的记录，<strong>不会删除磁盘上的实际文件</strong>。</div>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('delete-directory-modal').close()">取消</button>
            <button type="button" class="btn btn-error" id="confirm-delete-directory-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                <i class="bi bi-trash mr-2" aria-label="删除图标"></i>
                删除目录
            </button>
        </div>
    </div>
</dialog>

<!-- 目录浏览模态框 -->
<dialog id="directory-browser-modal" class="modal bg-base-100/80 backdrop-blur-sm">
    <div class="modal-box w-11/12 max-w-2xl h-5/6 max-h-[600px] flex flex-col bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>
        <h3 class="font-bold text-xl mb-4">选择目录</h3>

        <!-- 面包屑导航 -->
        <div class="mb-4">
            <div class="breadcrumbs text-sm">
                <ul id="directory-breadcrumbs">
                    <!-- 面包屑将在这里动态生成 -->
                </ul>
            </div>
        </div>

        <!-- 当前选中路径显示 -->
        <div class="mb-4">
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">当前选中路径：</span>
                </label>
                <input type="text" class="input input-bordered input-sm" id="selected-path-display" readonly>
            </div>
        </div>

        <!-- 目录列表容器 -->
        <div class="flex-1 overflow-hidden flex flex-col">
            <!-- 加载状态 -->
            <div class="flex justify-center items-center py-8 hidden" id="directory-browser-loading">
                <span class="loading loading-spinner loading-lg text-primary"></span>
                <span class="ml-3">加载中...</span>
            </div>

            <!-- 错误状态 -->
            <div class="text-center py-8 hidden" id="directory-browser-error">
                <i class="bi bi-exclamation-triangle text-error mb-4" style="font-size: 4rem;" aria-label="错误图标"></i>
                <p class="text-error font-medium" id="directory-browser-error-message">加载失败</p>
                <button type="button" class="btn btn-outline btn-sm mt-3" id="retry-load-directories">
                    <i class="bi bi-arrow-clockwise mr-2" aria-label="重试图标"></i>
                    重试
                </button>
            </div>

            <!-- 目录列表 -->
            <div class="overflow-y-auto flex-1" id="directory-list-container">
                <div class="space-y-1" id="directory-list">
                    <!-- 目录项将在这里动态生成 -->
                </div>
            </div>
        </div>

        <!-- 模态框操作按钮 -->
        <div class="modal-action mt-4">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('directory-browser-modal').close()">取消</button>
            <button type="button" class="btn btn-primary" id="confirm-path-selection" disabled>
                <i class="bi bi-check mr-2" aria-label="确认图标"></i>
                确认选择
            </button>
        </div>
    </div>
</dialog>


{% endblock %}

{% block extra_js %}
<script src="/static/js/directory-browser.js?=v0.9.25"></script>
<script src="/static/js/directories-manager.js?=v0.9.25"></script>
{% endblock %}
