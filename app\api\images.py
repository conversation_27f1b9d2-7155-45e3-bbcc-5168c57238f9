"""
图片管理 API 路由
提供图片虚拟化访问接口
"""
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from pathlib import Path
from app.core.database import get_db
from app.services.database_service import DatabaseService
from app.schemas.schemas import ImageListRequest
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/images",
    tags=["图片管理"],
    responses={404: {"description": "图片不存在"}}
)


@router.get("/info/{image_uuid}")
async def get_image_info(image_uuid: str, db: Session = Depends(get_db)):
    """
    获取图片的详细信息
    
    Args:
        image_uuid: 图片的虚拟 UUID
        db: 数据库会话
        
    Returns:
        图片信息
    """
    try:
        db_service = DatabaseService(db)
        v_image = db_service.get_image_by_uuid(image_uuid)
        
        if not v_image:
            raise HTTPException(status_code=404, detail="图片不存在")
        
        # 检查文件是否存在
        image_path = Path(v_image.real_file_path)
        file_exists = image_path.exists()
        
        return {
            "success": True,
            "image": {
                "uuid": v_image.uuid,
                "real_file_path": v_image.real_file_path,
                "image_type": v_image.image_type,
                "file_exists": file_exists,
                "file_size": image_path.stat().st_size if file_exists else None,
                "created_at": v_image.created_at,
                "updated_at": v_image.updated_at
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片信息时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片信息失败: {str(e)}")


@router.get("/{image_uuid}")
async def get_image_by_uuid(image_uuid: str, db: Session = Depends(get_db)):
    """
    通过虚拟 UUID 访问图片文件

    Args:
        image_uuid: 图片的虚拟 UUID
        db: 数据库会话

    Returns:
        图片文件响应
    """
    try:
        db_service = DatabaseService(db)
        v_image = db_service.get_image_by_uuid(image_uuid)

        if not v_image:
            raise HTTPException(status_code=404, detail="图片不存在")

        # 检查文件是否存在
        image_path = Path(v_image.real_file_path)
        if not image_path.exists():
            logger.error(f"图片文件不存在: {v_image.real_file_path}")
            raise HTTPException(status_code=404, detail="图片文件不存在")

        # 返回图片文件
        return FileResponse(
            path=str(image_path),
            media_type=f"image/{image_path.suffix[1:]}",  # 去掉点号
            filename=image_path.name
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片失败: {str(e)}")


@router.post("/list")
async def list_images(request: ImageListRequest, db: Session = Depends(get_db)):
    """
    获取图片列表

    Args:
        request: 图片列表请求参数
        db: 数据库会话

    Returns:
        图片列表
    """
    try:
        from app.models.models import VImage

        query = db.query(VImage)

        if request.image_type:
            query = query.filter(VImage.image_type == request.image_type)

        total_count = query.count()
        images = query.offset(request.offset).limit(request.limit).all()

        return {
            "success": True,
            "images": [
                {
                    "uuid": img.uuid,
                    "real_file_path": img.real_file_path,
                    "image_type": img.image_type,
                    "created_at": img.created_at,
                    "updated_at": img.updated_at
                }
                for img in images
            ],
            "total_count": total_count,
            "limit": request.limit,
            "offset": request.offset
        }

    except Exception as e:
        logger.error(f"获取图片列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片列表失败: {str(e)}")
