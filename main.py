"""
FastAPI 媒体管理器主应用
"""
from fastapi import FastAPI, Depends, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from app.core.database import get_db, engine
from app.models.base import BaseModel
from app.services.database_service import DatabaseService

# 导入 API 路由模块
from app.api import tags, genres, series, actors, directories, images, movies, favorites, mappings, configs
from app.schemas.schemas import BaseResponse

import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="媒体管理器 API",
    description="媒体管理器系统",
    version="0.9.15"
)

# 配置静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 配置模板引擎
templates = Jinja2Templates(directory="templates")

# 创建数据库表
BaseModel.metadata.create_all(bind=engine)

# 注册 API 路由（添加 /api 前缀）
app.include_router(movies.router, prefix="/api")
app.include_router(tags.router, prefix="/api")
app.include_router(genres.router, prefix="/api")
app.include_router(series.router, prefix="/api")
app.include_router(actors.router, prefix="/api")
app.include_router(directories.router, prefix="/api")
app.include_router(images.router, prefix="/api")
app.include_router(favorites.router, prefix="/api")
app.include_router(mappings.router, prefix="/api")
app.include_router(configs.router, prefix="/api")


# ========== 前端路由 ==========

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """仪表板页面"""
    return templates.TemplateResponse("dashboard.html", {"request": request})


@app.get("/movies", response_class=HTMLResponse)
async def movies_page(request: Request):
    """影片库页面"""
    return templates.TemplateResponse("movies.html", {"request": request})


@app.get("/favorites", response_class=HTMLResponse)
async def favorites_page(request: Request):
    """收藏页面"""
    return templates.TemplateResponse("favorites.html", {"request": request})


@app.get("/movies/info/{movie_id}", response_class=HTMLResponse)
async def movie_detail_page(request: Request, movie_id: int):
    """影片详情页面（通过ID）"""
    return templates.TemplateResponse("movie_detail.html", {
        "request": request,
        "movie_id": movie_id,
        "access_type": "id"
    })



@app.get("/movies/info/{movie_id}/edit", response_class=HTMLResponse)
async def movie_edit_page(request: Request, movie_id: int):
    """影片编辑页面（通过ID）"""
    return templates.TemplateResponse("movie_edit.html", {
        "request": request,
        "movie_id": movie_id,
        "access_type": "id"
    })



@app.get("/tags", response_class=HTMLResponse)
async def tags_page(request: Request):
    """标签管理页面"""
    return templates.TemplateResponse("tags_manager.html", {"request": request})


@app.get("/genres", response_class=HTMLResponse)
async def genres_page(request: Request):
    """分类管理页面"""
    return templates.TemplateResponse("genres_manager.html", {"request": request})


@app.get("/actors", response_class=HTMLResponse)
async def actors_page(request: Request):
    """演员管理页面"""
    return templates.TemplateResponse("actors_manager.html", {"request": request})


@app.get("/series", response_class=HTMLResponse)
async def series_page(request: Request):
    """系列管理页面"""
    return templates.TemplateResponse("series_manager.html", {"request": request})


@app.get("/directories", response_class=HTMLResponse)
async def directories_page(request: Request):
    """目录管理页面"""
    return templates.TemplateResponse("directories_manager.html", {"request": request})


@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    """设置页面"""
    return templates.TemplateResponse("settings.html", {"request": request})


# ========== API 路由 ==========

@app.get("/api", response_model=BaseResponse)
async def api_root():
    """API 根路径，返回 API 信息"""
    return BaseResponse(
        success=True,
        message="媒体管理器 API",
        data={
            "version": "0.2.0",
            "description": "类似 Jellyfin 的媒体管理器系统",
            "docs_url": "/docs",
            "redoc_url": "/redoc"
        }
    )


@app.get("/api/health", response_model=BaseResponse)
async def health_check():
    """健康检查端点"""
    return BaseResponse(
        success=True,
        message="API 运行正常",
        data={"status": "healthy"}
    )


@app.get("/api/database/stats", response_model=BaseResponse)
async def get_database_stats(db: Session = Depends(get_db)):
    """获取数据库统计信息"""
    try:
        db_service = DatabaseService(db)
        stats = db_service.get_database_stats()

        return BaseResponse(
            success=True,
            message="获取数据库统计信息成功",
            data=stats
        )

    except Exception as e:
        logger.error(f"获取数据库统计信息时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)


# uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload