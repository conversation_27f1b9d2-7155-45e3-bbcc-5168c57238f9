"""
配置管理 API 路由
提供系统配置的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.config_service import ConfigService
from app.schemas.schemas import (
    ConfigCreate, ConfigUpdate, ConfigResponse, BaseResponse,
    VideoExtensionsConfigRequest, VideoExtensionsConfigResponse
)
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/configs",
    tags=["配置管理"],
    responses={404: {"description": "配置不存在"}}
)


@router.get("/{config_key}", response_model=BaseResponse)
async def get_config(config_key: str, db: Session = Depends(get_db)):
    """
    获取指定配置项
    
    Args:
        config_key: 配置键名
        db: 数据库会话
        
    Returns:
        配置项信息
    """
    try:
        config_service = ConfigService(db)
        config = config_service.get_config(config_key)
        
        if not config:
            raise HTTPException(status_code=404, detail="配置项不存在")
        
        return BaseResponse(
            success=True,
            message="获取配置项成功",
            data=ConfigResponse.model_validate(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置项时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置项失败: {str(e)}")


@router.post("/", response_model=BaseResponse)
async def create_config(config_data: ConfigCreate, db: Session = Depends(get_db)):
    """
    创建配置项
    
    Args:
        config_data: 配置创建数据
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        config_service = ConfigService(db)
        config = config_service.create_config(config_data)
        
        if not config:
            raise HTTPException(status_code=400, detail="配置键已存在")
        
        return BaseResponse(
            success=True,
            message="配置项创建成功",
            data=ConfigResponse.model_validate(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建配置项时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建配置项失败: {str(e)}")


@router.put("/{config_key}", response_model=BaseResponse)
async def update_config(config_key: str, config_data: ConfigUpdate, db: Session = Depends(get_db)):
    """
    更新配置项
    
    Args:
        config_key: 配置键名
        config_data: 更新数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        config_service = ConfigService(db)
        config = config_service.update_config(config_key, config_data)
        
        if not config:
            raise HTTPException(status_code=404, detail="配置项不存在")
        
        return BaseResponse(
            success=True,
            message="配置项更新成功",
            data=ConfigResponse.model_validate(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新配置项时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新配置项失败: {str(e)}")


@router.delete("/{config_key}", response_model=BaseResponse)
async def delete_config(config_key: str, db: Session = Depends(get_db)):
    """
    删除配置项
    
    Args:
        config_key: 配置键名
        db: 数据库会话
        
    Returns:
        删除结果
    """
    try:
        config_service = ConfigService(db)
        success = config_service.delete_config(config_key)
        
        if not success:
            raise HTTPException(status_code=404, detail="配置项不存在")
        
        return BaseResponse(
            success=True,
            message="配置项删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除配置项时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除配置项失败: {str(e)}")


@router.get("/", response_model=BaseResponse)
async def get_all_configs(db: Session = Depends(get_db)):
    """
    获取所有配置项
    
    Args:
        db: 数据库会话
        
    Returns:
        所有配置项列表
    """
    try:
        config_service = ConfigService(db)
        configs = config_service.get_all_configs()
        
        config_responses = [ConfigResponse.model_validate(config) for config in configs]
        
        return BaseResponse(
            success=True,
            message="获取配置项列表成功",
            data=config_responses
        )
        
    except Exception as e:
        logger.error(f"获取配置项列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置项列表失败: {str(e)}")


# 视频扩展名专用接口
@router.get("/video-extensions/current", response_model=BaseResponse)
async def get_video_extensions(db: Session = Depends(get_db)):
    """
    获取当前视频扩展名配置
    
    Args:
        db: 数据库会话
        
    Returns:
        视频扩展名配置
    """
    try:
        config_service = ConfigService(db)
        current_extensions = config_service.get_video_extensions()
        
        response_data = VideoExtensionsConfigResponse(
            extensions=current_extensions,
            default_extensions=config_service.DEFAULT_VIDEO_EXTENSIONS
        )
        
        return BaseResponse(
            success=True,
            message="获取视频扩展名配置成功",
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"获取视频扩展名配置时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取视频扩展名配置失败: {str(e)}")


@router.post("/video-extensions/update", response_model=BaseResponse)
async def update_video_extensions(extensions_data: VideoExtensionsConfigRequest, db: Session = Depends(get_db)):
    """
    更新视频扩展名配置
    
    Args:
        extensions_data: 视频扩展名配置数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        config_service = ConfigService(db)
        success = config_service.set_video_extensions(extensions_data)
        
        if not success:
            raise HTTPException(status_code=500, detail="更新视频扩展名配置失败")
        
        # 返回更新后的配置
        current_extensions = config_service.get_video_extensions()
        response_data = VideoExtensionsConfigResponse(
            extensions=current_extensions,
            default_extensions=config_service.DEFAULT_VIDEO_EXTENSIONS
        )
        
        return BaseResponse(
            success=True,
            message="视频扩展名配置更新成功",
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新视频扩展名配置时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新视频扩展名配置失败: {str(e)}")


