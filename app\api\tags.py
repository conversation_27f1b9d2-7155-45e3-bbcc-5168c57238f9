"""
标签管理 API 路由
提供标签的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.management_service import TagService
from app.schemas.schemas import (
    TagCreate, TagUpdate, TagResponse, TagListResponse, TagListRequest, BaseResponse,
    BatchDeleteRequest, BatchDeleteResponse, UnifiedDeleteRequest,
    MergeRequest, MergeResult, MergeCheckResponse,
    RenameConflictCheckRequest, RenameConflictCheckResponse
)
from typing import List
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/tags",
    tags=["标签管理"],
    responses={404: {"description": "标签不存在"}}
)


@router.post("/add", response_model=BaseResponse)
async def create_tag(tag_data: TagCreate, db: Session = Depends(get_db)):
    """
    创建新标签
    
    Args:
        tag_data: 标签创建数据
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        tag_service = TagService(db)
        tag = tag_service.create_tag(tag_data)
        
        if not tag:
            raise HTTPException(status_code=400, detail="标签名称已存在")
        
        return BaseResponse(
            success=True,
            message="标签创建成功",
            data=TagResponse.model_validate(tag)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建标签时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建标签失败: {str(e)}")


@router.post("/list", response_model=TagListResponse)
async def get_tags(request: TagListRequest, db: Session = Depends(get_db)):
    """
    获取标签列表

    Args:
        request: 标签列表请求参数
        db: 数据库会话

    Returns:
        标签列表
    """
    try:
        # 参数验证
        if request.limit < 1 or request.limit > 1000:
            raise HTTPException(status_code=400, detail="limit 必须在 1-1000 之间")
        if request.offset < 0:
            raise HTTPException(status_code=400, detail="offset 必须大于等于 0")

        tag_service = TagService(db)
        tags, total_count = tag_service.get_tags(
            limit=request.limit,
            offset=request.offset,
            search=request.search
        )

        # 为每个标签添加电影数量
        tag_responses = []
        for tag in tags:
            tag_info = tag_service.get_tag_with_movie_count(tag.id)
            tag_response = TagResponse.model_validate(tag)
            tag_response.movie_count = tag_info["movie_count"] if tag_info else 0
            tag_responses.append(tag_response)

        return TagListResponse(
            success=True,
            message=f"获取到 {len(tags)} 个标签",
            data=tag_responses,
            total_count=total_count,
            limit=request.limit,
            offset=request.offset
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取标签列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取标签列表失败: {str(e)}")


@router.get("/info/{tag_id}", response_model=BaseResponse)
async def get_tag(tag_id: int, db: Session = Depends(get_db)):
    """
    获取指定标签详情
    
    Args:
        tag_id: 标签 ID
        db: 数据库会话
        
    Returns:
        标签详情
    """
    try:
        tag_service = TagService(db)
        tag_info = tag_service.get_tag_with_movie_count(tag_id)
        
        if not tag_info:
            raise HTTPException(status_code=404, detail="标签不存在")
        
        tag_response = TagResponse.model_validate(tag_info["tag"])
        tag_response.movie_count = tag_info["movie_count"]
        
        return BaseResponse(
            success=True,
            message="获取标签详情成功",
            data=tag_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取标签详情时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取标签详情失败: {str(e)}")


@router.put("/edit/{tag_id}", response_model=BaseResponse)
async def update_tag(tag_id: int, tag_data: TagUpdate, db: Session = Depends(get_db)):
    """
    更新标签信息
    
    Args:
        tag_id: 标签 ID
        tag_data: 更新数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        tag_service = TagService(db)
        tag = tag_service.update_tag(tag_id, tag_data)
        
        if not tag:
            # 检查是否是因为标签不存在
            existing_tag = tag_service.get_tag_by_id(tag_id)
            if not existing_tag:
                raise HTTPException(status_code=404, detail="标签不存在")
            else:
                raise HTTPException(status_code=400, detail="标签名称已存在")
        
        return BaseResponse(
            success=True,
            message="标签更新成功",
            data=TagResponse.model_validate(tag)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新标签时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新标签失败: {str(e)}")


@router.delete("", response_model=BaseResponse)
async def delete_tags(request: UnifiedDeleteRequest, db: Session = Depends(get_db)):
    """
    统一删除标签（支持单项和批量删除）

    Args:
        request: 统一删除请求，支持单个ID或ID列表
        db: 数据库会话

    Returns:
        删除结果
    """
    try:
        tag_service = TagService(db)
        result = tag_service.unified_delete_tags(request)

        if isinstance(result, tuple):
            # 单项删除结果
            success, message = result
            if not success:
                if "不存在" in message:
                    raise HTTPException(status_code=404, detail=message)
                else:
                    raise HTTPException(status_code=400, detail=message)

            return BaseResponse(
                success=True,
                message=message
            )
        else:
            # 批量删除结果
            batch_result = result
            if batch_result.failed_count == 0:
                message = f"成功删除 {batch_result.success_count} 个标签"
            elif batch_result.success_count == 0:
                message = f"删除失败，{batch_result.failed_count} 个标签无法删除"
            else:
                message = f"成功删除 {batch_result.success_count} 个标签，{batch_result.failed_count} 个失败"

            return BaseResponse(
                success=True,
                message=message,
                data=batch_result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除标签时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除标签失败: {str(e)}")


@router.post("/check-rename-conflict", response_model=RenameConflictCheckResponse)
async def check_rename_conflict(request: RenameConflictCheckRequest, db: Session = Depends(get_db)):
    """
    检查标签重命名是否会产生冲突

    Args:
        request: 重命名冲突检查请求
        db: 数据库会话

    Returns:
        冲突检查结果
    """
    try:
        tag_service = TagService(db)
        result = tag_service.check_rename_conflict(request.item_id, request.new_name)

        return RenameConflictCheckResponse(
            has_conflict=result["has_conflict"],
            can_auto_merge=result["can_auto_merge"],
            conflict_info=result.get("conflict_info"),
            message=result["message"]
        )

    except Exception as e:
        logger.error(f"检查标签重命名冲突时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")


@router.post("/check-merge-conflict", response_model=MergeCheckResponse)
async def check_merge_conflict(request: dict, db: Session = Depends(get_db)):
    """
    检查标签合并是否会产生冲突

    Args:
        request: 包含 target_name 和 source_ids 的请求体
        db: 数据库会话

    Returns:
        合并冲突检查结果
    """
    try:
        target_name = request.get("target_name")
        source_ids = request.get("source_ids")

        if not target_name:
            raise HTTPException(status_code=400, detail="target_name 是必需的")
        if not source_ids or not isinstance(source_ids, list):
            raise HTTPException(status_code=400, detail="source_ids 必须是非空列表")

        tag_service = TagService(db)
        result = tag_service.check_merge_conflict(target_name, source_ids)

        return MergeCheckResponse(
            can_merge=result["can_merge"],
            conflict_info=result.get("conflict_info"),
            message=result["message"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查标签合并冲突时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")


@router.post("/merge", response_model=MergeResult)
async def merge_tags(request: MergeRequest, db: Session = Depends(get_db)):
    """
    合并多个标签

    Args:
        request: 合并请求
        db: 数据库会话

    Returns:
        合并结果
    """
    try:
        tag_service = TagService(db)
        result = tag_service.merge_tags(
            source_ids=request.source_ids,
            target_name=request.target_name,
            target_description=request.target_description
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])

        return MergeResult(
            success=result["success"],
            target_item_id=result["target_item_id"],
            target_item_name=result["target_item_name"],
            merged_count=result["merged_count"],
            total_movies_affected=result["total_movies_affected"],
            message=result["message"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"合并标签时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"合并失败: {str(e)}")
