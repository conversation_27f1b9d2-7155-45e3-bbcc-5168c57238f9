/**
 * 仪表板页面 JavaScript
 * 处理轮播图、目录展示、最近添加影片等功能
 */

class Dashboard {
    constructor() {
        this.api = window.api;
        this.currentSlide = 0;
        this.carouselInterval = null;
        this.carouselAutoPlay = true;
        this.carouselDelay = 10000; // 10秒自动切换

        this.init();
    }

    /**
     * 初始化仪表板
     */
    async init() {
        try {
            // 绑定事件
            this.bindEvents();
            
            // 加载数据
            await this.loadDashboardData();
            
            // 隐藏加载状态
            this.hideLoading();
            
        } catch (error) {
            console.error('初始化仪表板失败:', error);
            this.showError('初始化仪表板失败');
            this.hideLoading();
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {


        // 轮播图鼠标悬停暂停自动播放
        const carousel = document.getElementById('latest-movies-carousel');
        if (carousel) {
            carousel.addEventListener('mouseenter', () => this.pauseCarousel());
            carousel.addEventListener('mouseleave', () => this.resumeCarousel());
        }

        // 目录容器横向滚动
        const directoriesContainer = document.getElementById('directories-container');
        if (directoriesContainer) {
            this.initHorizontalScroll(directoriesContainer);
        }
    }

    /**
     * 加载仪表板数据
     */
    async loadDashboardData() {
        try {
            // 并行加载各种数据
            const [latestMovies, recentMovies, directories, stats] = await Promise.all([
                this.loadLatestMovies(),
                this.loadRecentMovies(),
                this.loadDirectories(),
                this.loadStatistics()
            ]);

            // 渲染各个区域
            this.renderLatestMoviesCarousel(latestMovies);
            this.renderDirectories(directories);
            this.renderRecentMovies(recentMovies);
            this.renderStatistics(stats);

        } catch (error) {
            console.error('加载仪表板数据失败:', error);
            throw error;
        }
    }

    /**
     * 加载最新影片（用于轮播）
     */
    async loadLatestMovies() {
        const response = await this.api.post('/movies/list', {
            limit: 10,
            offset: 0,
            sort_by: 'year',
            sort_order: 'desc'
        });

        if (response.success) {
            return response.data;
        } else {
            throw new Error(response.message || '获取最新影片失败');
        }
    }

    /**
     * 加载最近添加的影片
     */
    async loadRecentMovies() {
        const response = await this.api.post('/movies/list', {
            limit: 24,
            offset: 0,
            sort_by: 'created_at',
            sort_order: 'desc'
        });

        if (response.success) {
            return response.data;
        } else {
            throw new Error(response.message || '获取最近添加影片失败');
        }
    }

    /**
     * 加载媒体目录
     */
    async loadDirectories() {
        const response = await this.api.post('/directories/list', {
            enabled_only: true,
            with_cover: true
        });

        if (response.success) {
            return response.data;
        } else {
            throw new Error(response.message || '获取媒体目录失败');
        }
    }

    /**
     * 加载统计信息
     */
    async loadStatistics() {
        const [dbStats, favoritesResponse] = await Promise.all([
            this.api.get('/database/stats'),
            this.api.post('/favorites/list', { limit: 1, offset: 0 })
        ]);

        if (!dbStats.success) {
            throw new Error(dbStats.message || '获取数据库统计失败');
        }

        if (!favoritesResponse.success) {
            throw new Error(favoritesResponse.message || '获取收藏统计失败');
        }

        // 计算今日添加数量
        const todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0);
        const todayEnd = new Date();
        todayEnd.setHours(23, 59, 59, 999);

        const todayMoviesResponse = await this.api.post('/movies/list', {
            limit: 30, // API 最大限制为 100
            offset: 0,
            sort_by: 'created_at',
            sort_order: 'desc'
        });

        let todayCount = 0;
        if (todayMoviesResponse.success && todayMoviesResponse.data) {
            todayCount = todayMoviesResponse.data.filter(movie => {
                if (!movie.created_at) return false;
                try {
                    const movieDate = new Date(movie.created_at);
                    return movieDate >= todayStart && movieDate <= todayEnd;
                } catch (e) {
                    console.warn('解析电影创建时间失败:', movie.created_at, e);
                    return false;
                }
            }).length;
        }

        return {
            total_movies: dbStats.data.total_movies || 0,
            total_directories: 0, // 将在目录加载后更新
            total_favorites: favoritesResponse.total_count || 0,
            today_added: todayCount
        };
    }

    /**
     * 渲染最新影片轮播图
     */
    renderLatestMoviesCarousel(movies) {
        const carousel = document.getElementById('latest-movies-carousel');
        const indicators = document.getElementById('carousel-indicators');

        if (!carousel || !indicators || !movies || movies.length === 0) {
            return;
        }

        // 清空现有内容
        carousel.innerHTML = '';
        indicators.innerHTML = '';

        movies.forEach((movie, index) => {
            // 创建轮播项
            const slide = document.createElement('div');
            slide.className = `carousel-item relative w-full ${index === 0 ? 'block' : 'hidden'}`;
            slide.id = `slide${index}`;

            // 背景图片（fanart）
            const bgImage = movie.fanart_uuid
                ? `/api/images/${movie.fanart_uuid}`
                : '/static/images/no-poster.svg';

            slide.innerHTML = `
                <div class="relative w-full h-full bg-cover bg-center bg-no-repeat"
                     style="background-image: url('${bgImage}');">
                    <!-- 遮罩层 -->
                    <div class="absolute inset-0 bg-black/40"></div>

                    <!-- 内容 -->
                    <div class="absolute inset-0 flex items-center">
                        <div class="container mx-auto px-4">
                            <div class="max-w-2xl text-white">
                                <h3 class="text-2xl md:text-4xl font-bold mb-2">${this.escapeHtml(movie.title)}</h3>
                                ${movie.year ? `<p class="text-lg mb-2">${movie.year}</p>` : ''}
                                ${movie.rating ? `<div class="flex items-center gap-2 mb-4">
                                    <i class="bi bi-star-fill text-yellow-400"></i>
                                    <span>${movie.rating.toFixed(1)}</span>
                                </div>` : ''}
                                ${movie.plot ? `<p class="text-base-content/80 mb-4 line-clamp-3">${this.escapeHtml(movie.plot.substring(0, 150))}${movie.plot.length > 150 ? '...' : ''}</p>` : ''}
                                <button class="btn btn-primary" onclick="window.location.href='/movies/info/${movie.id}'">
                                    <i class="bi bi-play-fill"></i>
                                    查看详情
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 导航按钮 -->
                    <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2">
                        <button class="btn btn-circle btn-sm bg-black/50 border-none text-white hover:bg-black/70"
                                onclick="dashboard.previousSlide()">❮</button>
                        <button class="btn btn-circle btn-sm bg-black/50 border-none text-white hover:bg-black/70"
                                onclick="dashboard.nextSlide()">❯</button>
                    </div>
                </div>
            `;

            carousel.appendChild(slide);

            // 创建指示器
            const indicator = document.createElement('button');
            indicator.className = `btn btn-xs ${index === 0 ? 'btn-primary' : 'btn-outline'}`;
            indicator.onclick = () => this.goToSlide(index);
            indicators.appendChild(indicator);
        });

        // 启动自动播放
        this.startCarousel();
    }

    /**
     * 渲染媒体目录
     */
    renderDirectories(directories) {
        const container = document.getElementById('directories-container');

        if (!container || !directories || directories.length === 0) {
            container.innerHTML = '<div class="text-center text-base-content/70 py-8">暂无媒体目录</div>';
            return;
        }

        container.innerHTML = '';

        directories.forEach(directory => {
            const dirItem = document.createElement('div');
            dirItem.className = 'flex-shrink-0 w-40 cursor-pointer hover:scale-105 transition-transform duration-200';

            // 目录封面图片
            let coverImage = '/static/images/no-poster.svg';
            if (directory.cover_image_base64) {
                // 检查是否已经包含 data: 前缀
                if (directory.cover_image_base64.startsWith('data:')) {
                    coverImage = directory.cover_image_base64;
                } else {
                    coverImage = `data:image/jpeg;base64,${directory.cover_image_base64}`;
                }
            }

            dirItem.innerHTML = `
                <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow duration-200 relative overflow-hidden">
                    <figure class="aspect-[16/9] overflow-hidden relative">
                        <img src="${coverImage}"
                             alt="${this.escapeHtml(directory.name)}"
                             class="w-full h-full object-cover"
                             onerror="this.src='/static/images/no-poster.svg'">
                        <!-- 影片数量 badge -->
                        <div class="badge badge-primary badge-sm absolute top-2 right-2">
                            ${directory.file_count || 0}
                        </div>
                        <!-- 目录名称覆盖层 -->
                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
                            <h3 class="text-white text-xs font-semibold leading-tight truncate">${this.escapeHtml(directory.name)}</h3>
                        </div>
                    </figure>
                </div>
            `;

            // 点击跳转到影片库并过滤该目录
            dirItem.addEventListener('click', () => {
                window.location.href = `/movies?directory_id=${directory.id}`;
            });

            container.appendChild(dirItem);
        });

        // 更新统计信息中的目录数量
        const totalDirectoriesElement = document.getElementById('total-directories');
        if (totalDirectoriesElement) {
            totalDirectoriesElement.textContent = directories.length.toLocaleString();
        }
    }

    /**
     * 渲染最近添加影片
     */
    renderRecentMovies(movies) {
        const container = document.getElementById('recent-movies-grid');

        if (!container || !movies || movies.length === 0) {
            container.innerHTML = '<div class="col-span-full text-center text-base-content/70 py-8">暂无最近添加的影片</div>';
            return;
        }

        container.innerHTML = '';

        movies.forEach(movie => {
            const movieItem = document.createElement('div');
            movieItem.className = 'cursor-pointer hover:scale-105 transition-transform duration-200';

            // 海报图片
            const posterImage = movie.poster_uuid
                ? `/api/images/${movie.poster_uuid}`
                : '/static/images/no-poster.svg';

            movieItem.innerHTML = `
                <div class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow duration-200">
                    <figure class="aspect-[2/3] overflow-hidden">
                        <img src="${posterImage}"
                             alt="${this.escapeHtml(movie.title)}"
                             class="w-full h-full object-cover"
                             onerror="this.src='/static/images/no-poster.svg'">
                    </figure>
                    <div class="card-body p-2">
                        <h3 class="text-xs font-medium line-clamp-2" title="${this.escapeHtml(movie.title)}">
                            ${this.escapeHtml(movie.title)}
                        </h3>
                        ${movie.year ? `<p class="text-xs text-base-content/70">${movie.year}</p>` : ''}
                        ${movie.rating ? `<div class="flex items-center gap-1 text-xs">
                            <i class="bi bi-star-fill text-yellow-400"></i>
                            <span>${movie.rating.toFixed(1)}</span>
                        </div>` : ''}
                    </div>
                </div>
            `;

            // 点击跳转到影片详情
            movieItem.addEventListener('click', () => {
                window.location.href = `/movies/id/${movie.id}`;
            });

            container.appendChild(movieItem);
        });
    }

    /**
     * 渲染统计信息
     */
    renderStatistics(stats) {
        const elements = {
            'total-movies': stats.total_movies || 0,
            'total-favorites': stats.total_favorites || 0,
            'today-added': stats.today_added || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value.toLocaleString();
            }
        });

        // 目录数量在 renderDirectories 方法中更新
    }

    /**
     * 轮播图控制方法
     */
    startCarousel() {
        if (this.carouselAutoPlay) {
            this.carouselInterval = setInterval(() => {
                this.nextSlide();
            }, this.carouselDelay);
        }
    }

    pauseCarousel() {
        if (this.carouselInterval) {
            clearInterval(this.carouselInterval);
            this.carouselInterval = null;
        }
    }

    resumeCarousel() {
        if (this.carouselAutoPlay && !this.carouselInterval) {
            this.startCarousel();
        }
    }

    nextSlide() {
        const slides = document.querySelectorAll('#latest-movies-carousel .carousel-item');
        if (slides.length === 0) return;

        this.currentSlide = (this.currentSlide + 1) % slides.length;
        this.updateCarousel();
    }

    previousSlide() {
        const slides = document.querySelectorAll('#latest-movies-carousel .carousel-item');
        if (slides.length === 0) return;

        this.currentSlide = (this.currentSlide - 1 + slides.length) % slides.length;
        this.updateCarousel();
    }

    goToSlide(index) {
        this.currentSlide = index;
        this.updateCarousel();
    }

    updateCarousel() {
        const slides = document.querySelectorAll('#latest-movies-carousel .carousel-item');
        const indicators = document.querySelectorAll('#carousel-indicators button');

        slides.forEach((slide, index) => {
            slide.classList.toggle('hidden', index !== this.currentSlide);
            slide.classList.toggle('block', index === this.currentSlide);
        });

        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('btn-primary', index === this.currentSlide);
            indicator.classList.toggle('btn-outline', index !== this.currentSlide);
        });
    }

    /**
     * 初始化横向滚动
     */
    initHorizontalScroll(container) {
        container.addEventListener('wheel', (e) => {
            if (e.deltaY !== 0) {
                e.preventDefault();
                container.scrollLeft += e.deltaY;
            }
        });
    }

    /**
     * 刷新仪表板
     */
    async refreshDashboard() {
        this.showLoading();
        try {
            await this.loadDashboardData();
            if (window.toast) {
                window.toast.success('仪表板数据已刷新');
            }
        } catch (error) {
            console.error('刷新仪表板失败:', error);
            if (window.toast) {
                window.toast.error('刷新失败，请稍后重试');
            }
            this.showError('刷新仪表板失败');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const errorAlert = document.getElementById('error-alert');
        const errorMessage = document.getElementById('error-message');
        
        if (errorAlert && errorMessage) {
            errorMessage.textContent = message;
            errorAlert.classList.remove('hidden');
        }
    }

    /**
     * HTML 转义
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 全局变量，供模板中的按钮调用
let dashboard;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new Dashboard();
});
