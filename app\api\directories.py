"""
目录管理 API 路由
提供媒体目录的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel as PydanticBaseModel
from app.core.database import get_db
from app.services.directory_service import DirectoryService
from app.models.models import Directory
from app.schemas.schemas import (
    DirectoryListRequest, DirectoryDeleteRequest, DirectoryScanRequest,
    DirectoryPathValidateRequest, DirectoryPathValidateResponse, BaseResponse
)
import logging
import os
from pathlib import Path
import platform
from typing import Optional, List

logger = logging.getLogger(__name__)

# Pydantic 模型
class DirectoryPathData(PydanticBaseModel):
    """路径数据模型"""
    id: Optional[int] = None  # 现有路径的ID，新路径为None
    path: str
    enabled: bool = True
    isNew: Optional[bool] = None  # 前端标识，后端忽略

class DirectoryCreate(PydanticBaseModel):
    name: str
    enabled: bool = True
    paths: Optional[List[DirectoryPathData]] = None  # 多路径字段

class DirectoryUpdate(PydanticBaseModel):
    name: Optional[str] = None
    enabled: Optional[bool] = None
    paths: Optional[List[DirectoryPathData]] = None  # 多路径字段



class DirectoryItem(PydanticBaseModel):
    name: str
    path: str
    is_directory: bool
    size: Optional[int] = None
    modified_time: Optional[str] = None

class DirectoryBrowseRequest(PydanticBaseModel):
    path: Optional[str] = None

# 创建路由器
router = APIRouter(
    prefix="/directories",
    tags=["目录管理"],
    responses={404: {"description": "目录不存在"}}
)


@router.post("/add", response_model=BaseResponse)
async def create_directory(directory: DirectoryCreate, db: Session = Depends(get_db)):
    """创建新的媒体目录（支持多路径）"""
    try:
        # 验证输入数据
        if not directory.name or not directory.name.strip():
            raise HTTPException(status_code=400, detail="目录名称不能为空")

        # 验证目录名称格式
        name = directory.name.strip()
        if len(name) < 1 or len(name) > 100:
            raise HTTPException(status_code=400, detail="目录名称长度必须在1-100字符之间")

        # 检查特殊字符（允许中文、英文、数字、空格、下划线、连字符）
        import re
        if not re.match(r'^[\w\s\u4e00-\u9fff-]+$', name):
            raise HTTPException(status_code=400, detail="目录名称只能包含中文、英文、数字、空格、下划线和连字符")

        dir_service = DirectoryService(db)

        # 处理路径数据
        paths_data = []
        if directory.paths:
            # 使用多路径格式
            for path_data in directory.paths:
                if path_data.path and path_data.path.strip():
                    paths_data.append({
                        'path': path_data.path.strip(),
                        'enabled': path_data.enabled
                    })

        if not paths_data:
            raise HTTPException(status_code=400, detail="至少需要一个有效的目录路径")

        new_directory = dir_service.create_directory_with_paths(
            name=name,
            enabled=directory.enabled,
            paths=paths_data
        )

        if new_directory:
            # 获取路径信息
            paths_info = []
            for path in new_directory.paths:
                paths_info.append({
                    "id": path.id,
                    "path": path.path,
                    "enabled": path.enabled,
                    "is_primary": path.is_primary
                })

            return BaseResponse(
                success=True,
                message="目录创建成功",
                data={
                    "id": new_directory.id,
                    "name": new_directory.name,
                    "enabled": new_directory.enabled,
                    "paths": paths_info,
                    "file_count": 0,
                    "last_scan": None,
                    "created_at": new_directory.created_at.isoformat() if new_directory.created_at else None
                }
            )
        else:
            raise HTTPException(status_code=400, detail="目录创建失败")

    except ValueError as e:
        # 处理名称或路径重复的错误
        logger.warning(f"创建目录验证失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise
    except Exception as e:
        logger.error(f"创建目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建目录失败: {str(e)}")


@router.post("/list")
async def get_directories(request: DirectoryListRequest, db: Session = Depends(get_db)):
    """获取所有目录"""
    try:
        dir_service = DirectoryService(db)
        directories = dir_service.get_all_directories(enabled_only=request.enabled_only)

        # 获取每个目录的文件数量
        from app.services.database_service import DatabaseService
        db_service = DatabaseService(db)

        directory_list = []
        for d in directories:
            # 获取该目录下的影片数量
            file_count = db_service.get_movies_count_by_directory(d.id)

            # 构建路径信息
            paths_data = []
            for path in d.paths:
                paths_data.append({
                    "id": path.id,
                    "path": path.path,
                    "enabled": path.enabled,
                    "is_primary": path.is_primary,
                    "created_at": path.created_at.isoformat() if path.created_at else None,
                    "updated_at": path.updated_at.isoformat() if path.updated_at else None
                })

            directory_data = {
                "id": d.id,
                "name": d.name,
                "paths": paths_data,
                "enabled": d.enabled,
                "file_count": file_count,
                "last_scan": d.last_scan_time.isoformat() if d.last_scan_time else None,
                "created_at": d.created_at.isoformat() if d.created_at else None
            }

            # 如果需要封面图片，添加封面数据
            if request.with_cover:
                directory_data["cover_image_base64"] = d.cover_image_base64
                directory_data["cover_generated_at"] = d.cover_generated_at.isoformat() if d.cover_generated_at else None

            directory_list.append(directory_data)

        return {
            "success": True,
            "data": directory_list,
            "count": len(directory_list)
        }

    except Exception as e:
        logger.error(f"获取目录列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取目录列表失败: {str(e)}")



@router.post("/regenerate-all-covers")
async def regenerate_all_covers(db: Session = Depends(get_db)):
    """重新生成所有目录的封面图片"""
    try:
        dir_service = DirectoryService(db)
        result = dir_service.regenerate_all_covers()

        return {
            "success": True,
            "message": "封面重新生成完成",
            "data": result
        }

    except Exception as e:
        logger.error(f"重新生成所有封面时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"重新生成封面失败: {str(e)}")


@router.post("/browse")
async def browse_directories(request: DirectoryBrowseRequest):
    """
    浏览服务器目录结构

    Args:
        request: 包含要浏览的目录路径的请求体

    Returns:
        目录列表和当前路径信息
    """
    try:
        # 获取安全的可浏览路径
        safe_paths = _get_safe_browsable_paths()

        # 从请求体中获取路径
        path = request.path

        # 如果没有指定路径，返回根目录列表
        if not path:
            return {
                "success": True,
                "data": {
                    "current_path": "",
                    "parent_path": None,
                    "items": [
                        {
                            "name": os.path.basename(safe_path) or safe_path,
                            "path": safe_path,
                            "is_directory": True,
                            "modified_time": None
                        }
                        for safe_path in safe_paths
                    ],
                    "breadcrumbs": [{"name": "根目录", "path": "", "is_current": True}],
                    "total_count": len(safe_paths)
                }
            }

        # 规范化路径
        normalized_path = os.path.normpath(path)

        # 检查路径是否在安全范围内
        if not _is_path_safe(normalized_path, safe_paths):
            raise HTTPException(status_code=403, detail="无权访问此路径")

        # 检查路径是否存在
        if not os.path.exists(normalized_path):
            raise HTTPException(status_code=404, detail="路径不存在")

        # 检查是否为目录
        if not os.path.isdir(normalized_path):
            raise HTTPException(status_code=400, detail="指定路径不是目录")

        # 获取目录内容
        items = []
        try:
            for item_name in os.listdir(normalized_path):
                item_path = os.path.join(normalized_path, item_name)

                # 跳过隐藏文件和系统文件
                if item_name.startswith('.'):
                    continue

                # 只处理目录
                if os.path.isdir(item_path):
                    try:
                        stat_info = os.stat(item_path)
                        items.append(DirectoryItem(
                            name=item_name,
                            path=item_path,
                            is_directory=True,
                            modified_time=str(int(stat_info.st_mtime))
                        ))
                    except (OSError, PermissionError):
                        # 跳过无法访问的目录
                        continue

        except PermissionError:
            raise HTTPException(status_code=403, detail="无权限访问此目录")

        # 按名称排序
        items.sort(key=lambda x: x.name.lower())

        # 生成面包屑导航
        breadcrumbs = _generate_breadcrumbs(normalized_path)

        # 获取父目录路径
        parent_path = os.path.dirname(normalized_path) if normalized_path != "/" else None
        if parent_path and not _is_path_safe(parent_path, safe_paths):
            parent_path = None

        return {
            "success": True,
            "data": {
                "current_path": normalized_path,
                "parent_path": parent_path,
                "items": [item.dict() for item in items],
                "breadcrumbs": breadcrumbs,
                "total_count": len(items)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"浏览目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"浏览目录失败: {str(e)}")


@router.get("/info/{directory_id}")
async def get_directory(directory_id: int, db: Session = Depends(get_db)):
    """获取指定目录"""
    try:
        dir_service = DirectoryService(db)
        directory = dir_service.get_directory_by_id(directory_id)
        
        if not directory:
            raise HTTPException(status_code=404, detail="目录不存在")
        
        # 构建路径信息
        paths_data = []
        for path in directory.paths:
            paths_data.append({
                "id": path.id,
                "path": path.path,
                "enabled": path.enabled,
                "is_primary": path.is_primary,
                "created_at": path.created_at.isoformat() if path.created_at else None,
                "updated_at": path.updated_at.isoformat() if path.updated_at else None
            })

        return {
            "success": True,
            "directory": {
                "id": directory.id,
                "name": directory.name,
                "paths": paths_data,
                "enabled": directory.enabled,
                "last_scan_time": directory.last_scan_time.isoformat() if directory.last_scan_time else None,
                "created_at": directory.created_at.isoformat() if directory.created_at else None,
                "updated_at": directory.updated_at.isoformat() if directory.updated_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取目录失败: {str(e)}")


@router.put("/edit/{directory_id}")
async def update_directory(directory_id: int, directory: DirectoryUpdate, db: Session = Depends(get_db)):
    """更新目录（支持多路径）"""
    try:
        # 验证输入数据
        if directory.name is not None:
            name = directory.name.strip()
            if not name:
                raise HTTPException(status_code=400, detail="目录名称不能为空")
            if len(name) < 1 or len(name) > 100:
                raise HTTPException(status_code=400, detail="目录名称长度必须在1-100字符之间")

            # 检查特殊字符
            import re
            if not re.match(r'^[\w\s\u4e00-\u9fff-]+$', name):
                raise HTTPException(status_code=400, detail="目录名称只能包含中文、英文、数字、空格、下划线和连字符")

        dir_service = DirectoryService(db)

        # 处理路径更新
        if directory.paths is not None:
            # 使用多路径更新方式
            paths_data = []
            for path_data in directory.paths:
                if path_data.path and path_data.path.strip():
                    paths_data.append({
                        'id': path_data.id,
                        'path': path_data.path.strip(),
                        'enabled': path_data.enabled
                    })

            if not paths_data:
                raise HTTPException(status_code=400, detail="至少需要一个有效的目录路径")

            updated_directory = dir_service.update_directory_with_paths(
                directory_id=directory_id,
                name=directory.name.strip() if directory.name else None,
                enabled=directory.enabled,
                paths=paths_data
            )
        else:
            # 只更新基本信息，不涉及路径
            update_data = {}
            if directory.name is not None:
                update_data['name'] = directory.name.strip()
            if directory.enabled is not None:
                update_data['enabled'] = directory.enabled

            updated_directory = dir_service.update_directory_basic_info(directory_id, **update_data)

        if not updated_directory:
            raise HTTPException(status_code=404, detail="目录不存在或更新失败")

        # 获取文件数量
        from app.services.database_service import DatabaseService
        db_service = DatabaseService(db)
        file_count = db_service.get_movies_count_by_directory(updated_directory.id)

        # 获取路径信息
        paths_info = []
        for path in updated_directory.paths:
            paths_info.append({
                "id": path.id,
                "path": path.path,
                "enabled": path.enabled,
                "is_primary": path.is_primary
            })

        return {
            "success": True,
            "message": "目录更新成功",
            "data": {
                "id": updated_directory.id,
                "name": updated_directory.name,
                "enabled": updated_directory.enabled,
                "paths": paths_info,
                "file_count": file_count,
                "last_scan": updated_directory.last_scan_time.isoformat() if updated_directory.last_scan_time else None,
                "created_at": updated_directory.created_at.isoformat() if updated_directory.created_at else None
            }
        }

    except ValueError as e:
        # 处理名称或路径重复的错误
        logger.warning(f"更新目录验证失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新目录失败: {str(e)}")


@router.delete("")
async def delete_directories(request: DirectoryDeleteRequest, db: Session = Depends(get_db)):
    """
    批量删除目录

    删除目录配置及其关联的所有影片数据，但不删除磁盘上的实际文件

    Args:
        request: 包含要删除的目录ID列表的请求
    """
    try:
        dir_service = DirectoryService(db)
        from app.services.database_service import DatabaseService
        db_service = DatabaseService(db)

        deleted_directories = []
        total_deleted_movies = 0
        failed_directories = []

        for directory_id in request.directories_ids:
            try:
                # 获取目录信息
                directory = dir_service.get_directory_by_id(directory_id)
                if not directory:
                    failed_directories.append({
                        "id": directory_id,
                        "reason": "目录不存在"
                    })
                    continue

                # 获取目录下的影片数量
                movie_count = db_service.get_movies_count_by_directory(directory.id)

                # 执行删除
                success = dir_service.delete_directory(directory_id)

                if success:
                    # 获取主路径用于显示
                    primary_path = next((path.path for path in directory.paths if path.is_primary),
                                      directory.paths[0].path if directory.paths else "未知路径")

                    deleted_directories.append({
                        "id": directory_id,
                        "name": directory.name,
                        "primary_path": primary_path,
                        "deleted_movies_count": movie_count
                    })
                    total_deleted_movies += movie_count
                else:
                    failed_directories.append({
                        "id": directory_id,
                        "reason": "删除操作失败"
                    })

            except Exception as e:
                logger.error(f"删除目录 {directory_id} 时发生错误: {e}")
                failed_directories.append({
                    "id": directory_id,
                    "reason": str(e)
                })

        # 构建响应消息
        success_count = len(deleted_directories)
        total_count = len(request.directories_ids)

        if success_count == total_count:
            message = f"成功删除 {success_count} 个目录"
        elif success_count > 0:
            message = f"成功删除 {success_count} 个目录，{len(failed_directories)} 个失败"
        else:
            message = "所有目录删除失败"

        if total_deleted_movies > 0:
            message += f"，同时删除了 {total_deleted_movies} 部影片的数据记录"

        return {
            "success": success_count > 0,
            "message": message,
            "deleted_directories": deleted_directories,
            "failed_directories": failed_directories,
            "total_deleted_movies": total_deleted_movies,
            "statistics": {
                "total_requested": total_count,
                "successful": success_count,
                "failed": len(failed_directories)
            }
        }

    except Exception as e:
        logger.error(f"批量删除目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量删除目录失败: {str(e)}")







@router.post("/scan")
async def scan_directories(
    request: DirectoryScanRequest,
    db: Session = Depends(get_db)
):
    """
    批量扫描目录（异步）

    支持以下扫描模式：
    1. 扫描指定目录：提供 directories_ids 列表
    2. 扫描所有启用目录：提供空的 directories_ids 列表

    Args:
        request: 扫描请求，包含目录ID列表和扫描模式
        db: 数据库会话

    Returns:
        扫描任务ID和基本信息
    """
    try:
        from app.services.async_scan_service import async_scan_service

        dir_service = DirectoryService(db)
        incremental = request.incremental

        # 根据请求参数确定要扫描的目录
        if request.directories_ids:
            # 扫描指定目录
            directories = []
            for directory_id in request.directories_ids:
                directory = dir_service.get_directory_by_id(directory_id)
                if directory and directory.enabled:
                    directories.append(directory)
                elif directory and not directory.enabled:
                    logger.warning(f"目录 {directory_id} 已禁用，跳过扫描")
                else:
                    logger.warning(f"目录 {directory_id} 不存在，跳过扫描")
        else:
            # 扫描所有启用的目录
            directories = dir_service.get_all_directories(enabled_only=True)

        if not directories:
            scan_type = "增量扫描" if incremental else "全量扫描"
            message = "没有启用的目录需要扫描" if not request.directories_ids else "没有有效的目录需要扫描"
            return {
                "success": True,
                "message": message,
                "scan_type": scan_type,
                "task_id": None,
                "directories_count": 0
            }

        # 启动异步扫描
        scan_type = "增量扫描" if incremental else "全量扫描"
        task_id = await async_scan_service.start_directory_scan(
            db, directories, incremental, scan_type
        )

        return {
            "success": True,
            "message": f"已启动{scan_type}任务",
            "scan_type": scan_type,
            "task_id": task_id,
            "directories_count": len(directories)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动扫描任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"启动扫描任务失败: {str(e)}")


@router.get("/scan/progress/{task_id}")
async def get_scan_progress(task_id: str):
    """
    获取扫描进度

    Args:
        task_id: 扫描任务ID

    Returns:
        扫描进度信息
    """
    try:
        from app.services.async_scan_service import async_scan_service

        progress = async_scan_service.get_scan_progress(task_id)
        if progress is None:
            raise HTTPException(status_code=404, detail="扫描任务不存在")

        return {
            "success": True,
            "progress": progress
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取扫描进度时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取扫描进度失败: {str(e)}")


@router.post("/scan/pause/{task_id}")
async def pause_scan(task_id: str):
    """
    暂停扫描任务

    Args:
        task_id: 扫描任务ID

    Returns:
        操作结果
    """
    try:
        from app.services.async_scan_service import async_scan_service

        success = async_scan_service.pause_scan(task_id)
        if not success:
            raise HTTPException(status_code=400, detail="无法暂停扫描任务")

        return {
            "success": True,
            "message": "扫描任务已暂停"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停扫描任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"暂停扫描任务失败: {str(e)}")


@router.post("/scan/resume/{task_id}")
async def resume_scan(task_id: str):
    """
    恢复扫描任务

    Args:
        task_id: 扫描任务ID

    Returns:
        操作结果
    """
    try:
        from app.services.async_scan_service import async_scan_service

        success = async_scan_service.resume_scan(task_id)
        if not success:
            raise HTTPException(status_code=400, detail="无法恢复扫描任务")

        return {
            "success": True,
            "message": "扫描任务已恢复"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复扫描任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"恢复扫描任务失败: {str(e)}")


@router.post("/scan/cancel/{task_id}")
async def cancel_scan(task_id: str):
    """
    取消扫描任务

    Args:
        task_id: 扫描任务ID

    Returns:
        操作结果
    """
    try:
        from app.services.async_scan_service import async_scan_service

        success = async_scan_service.cancel_scan(task_id)
        if not success:
            raise HTTPException(status_code=400, detail="无法取消扫描任务")

        return {
            "success": True,
            "message": "扫描任务已取消"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消扫描任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"取消扫描任务失败: {str(e)}")


@router.get("/scan/active")
async def get_active_scans():
    """
    获取所有活跃的扫描任务

    Returns:
        活跃扫描任务列表
    """
    try:
        from app.services.scan_progress_service import scan_progress_service

        active_tasks = scan_progress_service.get_all_active_tasks()

        return {
            "success": True,
            "active_tasks": active_tasks
        }

    except Exception as e:
        logger.error(f"获取活跃扫描任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取活跃扫描任务失败: {str(e)}")


@router.get("/stats")
async def get_directory_stats(db: Session = Depends(get_db)):
    """获取目录统计信息"""
    try:
        dir_service = DirectoryService(db)
        stats = dir_service.get_directory_stats()

        return {
            "success": True,
            "stats": stats
        }

    except Exception as e:
        logger.error(f"获取目录统计信息时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


def _get_safe_browsable_paths() -> List[str]:
    """
    获取安全的可浏览路径列表
    根据操作系统返回不同的安全路径
    """
    system = platform.system().lower()

    if system == "windows":
        # Windows系统的安全路径
        safe_paths = [
            "C:\\Users",
            "D:\\",
            "E:\\",
            "F:\\",
            "G:\\",
            "H:\\",
            "I:\\",
            "J:\\",
            "K:\\",
            "L:\\",
            "M:\\",
            "N:\\",
            "O:\\",
            "P:\\",
            "Q:\\",
            "R:\\",
            "S:\\",
            "T:\\",
            "U:\\",
            "V:\\",
            "W:\\",
            "X:\\",
            "Y:\\",
            "Z:\\"
        ]
        # 过滤存在的驱动器
        safe_paths = [path for path in safe_paths if os.path.exists(path)]
    else:
        # Linux/macOS系统的安全路径
        safe_paths = [
            "/home",
            "/media",
            "/mnt",
            "/opt",
            "/srv",
            "/var/lib",
            "/usr/local"
        ]
        # 过滤存在的路径
        safe_paths = [path for path in safe_paths if os.path.exists(path)]

        # 添加用户主目录
        home_dir = os.path.expanduser("~")
        if home_dir and os.path.exists(home_dir):
            safe_paths.append(home_dir)

    return safe_paths


def _is_path_safe(path: str, safe_paths: List[str]) -> bool:
    """
    检查路径是否在安全范围内
    """
    normalized_path = os.path.normpath(os.path.abspath(path))

    for safe_path in safe_paths:
        safe_normalized = os.path.normpath(os.path.abspath(safe_path))
        if normalized_path.startswith(safe_normalized):
            return True

    return False


def _generate_breadcrumbs(path: str) -> List[dict]:
    """
    生成面包屑导航
    """
    breadcrumbs = []
    parts = []

    # 分割路径
    if platform.system().lower() == "windows":
        # Windows路径处理
        if path.startswith("\\\\"):
            # UNC路径
            parts = path.split("\\")
        else:
            # 普通Windows路径
            drive, rest = os.path.splitdrive(path)
            if drive:
                parts = [drive] + rest.split("\\")
            else:
                parts = path.split("\\")
    else:
        # Unix-like路径处理
        parts = path.split("/")

    # 构建面包屑
    current_path = ""
    for i, part in enumerate(parts):
        if not part and i > 0:  # 跳过空部分（除了根目录）
            continue

        if i == 0:
            if platform.system().lower() == "windows":
                current_path = part if part else "\\"
                display_name = part if part else "根目录"
            else:
                current_path = "/"
                display_name = "根目录"
        else:
            if platform.system().lower() == "windows":
                current_path = os.path.join(current_path, part)
            else:
                current_path = current_path.rstrip("/") + "/" + part
            display_name = part

        breadcrumbs.append({
            "name": display_name,
            "path": current_path,
            "is_current": i == len(parts) - 1
        })

    return breadcrumbs


# ==================== 路径验证 API ====================

@router.post("/paths/validate")
async def validate_directory_path(
    path_data: DirectoryPathValidateRequest,
    db: Session = Depends(get_db)
):
    """验证目录路径"""
    try:
        dir_service = DirectoryService(db)

        # 验证路径
        validation_result = dir_service.validate_path(path_data.path)

        return {
            "success": True,
            "data": DirectoryPathValidateResponse(**validation_result)
        }

    except Exception as e:
        logger.error(f"验证目录路径时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"验证路径失败: {str(e)}")

