"""initial_schema

Revision ID: e3a6a4f0653b
Revises: 
Create Date: 2025-07-31 20:46:00.865966

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e3a6a4f0653b'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('actors',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('role', sa.String(length=255), nullable=True),
    sa.Column('biography', sa.Text(), nullable=True),
    sa.Column('actor_type', sa.String(length=50), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_actors_id'), 'actors', ['id'], unique=False)
    op.create_index(op.f('ix_actors_name'), 'actors', ['name'], unique=False)
    op.create_table('configs',
    sa.Column('config_key', sa.String(length=100), nullable=False),
    sa.Column('config_value', sa.Text(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_configs_config_key'), 'configs', ['config_key'], unique=True)
    op.create_index(op.f('ix_configs_id'), 'configs', ['id'], unique=False)
    op.create_table('directories',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('enabled', sa.Boolean(), nullable=True),
    sa.Column('last_scan_time', sa.DateTime(), nullable=True),
    sa.Column('cover_image_base64', sa.Text(), nullable=True),
    sa.Column('cover_generated_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_directories_id'), 'directories', ['id'], unique=False)
    op.create_table('genres',
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_genres_id'), 'genres', ['id'], unique=False)
    op.create_index(op.f('ix_genres_name'), 'genres', ['name'], unique=True)
    op.create_table('mapping_rules',
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.Column('original_value', sa.String(length=255), nullable=False),
    sa.Column('mapped_value', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('type', 'original_value', name='uq_mapping_type_original')
    )
    op.create_index(op.f('ix_mapping_rules_id'), 'mapping_rules', ['id'], unique=False)
    op.create_index(op.f('ix_mapping_rules_original_value'), 'mapping_rules', ['original_value'], unique=False)
    op.create_index(op.f('ix_mapping_rules_status'), 'mapping_rules', ['status'], unique=False)
    op.create_index(op.f('ix_mapping_rules_type'), 'mapping_rules', ['type'], unique=False)
    op.create_table('series',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_series_id'), 'series', ['id'], unique=False)
    op.create_index(op.f('ix_series_name'), 'series', ['name'], unique=True)
    op.create_table('tags',
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tags_id'), 'tags', ['id'], unique=False)
    op.create_index(op.f('ix_tags_name'), 'tags', ['name'], unique=True)
    op.create_table('v_images',
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('real_file_path', sa.String(length=500), nullable=False),
    sa.Column('image_type', sa.String(length=50), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_v_images_id'), 'v_images', ['id'], unique=False)
    op.create_index(op.f('ix_v_images_uuid'), 'v_images', ['uuid'], unique=True)
    op.create_table('directory_paths',
    sa.Column('directory_id', sa.Integer(), nullable=False),
    sa.Column('path', sa.String(length=500), nullable=False),
    sa.Column('is_primary', sa.Boolean(), nullable=False),
    sa.Column('enabled', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['directory_id'], ['directories.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('path')
    )
    op.create_index(op.f('ix_directory_paths_id'), 'directory_paths', ['id'], unique=False)
    op.create_table('movies',
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('original_title', sa.String(length=255), nullable=True),
    sa.Column('year', sa.Integer(), nullable=True),
    sa.Column('rating', sa.Float(), nullable=True),
    sa.Column('runtime', sa.Integer(), nullable=True),
    sa.Column('plot', sa.Text(), nullable=True),
    sa.Column('outline', sa.Text(), nullable=True),
    sa.Column('release_date', sa.DateTime(), nullable=True),
    sa.Column('premiered', sa.DateTime(), nullable=True),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('critic_rating', sa.Float(), nullable=True),
    sa.Column('sort_title', sa.String(length=255), nullable=True),
    sa.Column('trailer', sa.String(length=500), nullable=True),
    sa.Column('num', sa.String(length=50), nullable=True),
    sa.Column('lock_data', sa.Boolean(), nullable=True),
    sa.Column('date_added', sa.DateTime(), nullable=True),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('nfo_path', sa.String(length=500), nullable=True),
    sa.Column('poster_path', sa.String(length=500), nullable=True),
    sa.Column('fanart_path', sa.String(length=500), nullable=True),
    sa.Column('thumb_path', sa.String(length=500), nullable=True),
    sa.Column('series_id', sa.Integer(), nullable=True),
    sa.Column('directory_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['directory_id'], ['directories.id'], ),
    sa.ForeignKeyConstraint(['series_id'], ['series.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_movies_id'), 'movies', ['id'], unique=False)
    op.create_index(op.f('ix_movies_original_title'), 'movies', ['original_title'], unique=False)
    op.create_index(op.f('ix_movies_title'), 'movies', ['title'], unique=False)
    op.create_index(op.f('ix_movies_year'), 'movies', ['year'], unique=False)
    op.create_table('favorites',
    sa.Column('movie_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['movie_id'], ['movies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_favorites_id'), 'favorites', ['id'], unique=False)
    op.create_index(op.f('ix_favorites_movie_id'), 'favorites', ['movie_id'], unique=True)
    op.create_table('movie_actors',
    sa.Column('movie_id', sa.Integer(), nullable=False),
    sa.Column('actor_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['actor_id'], ['actors.id'], ),
    sa.ForeignKeyConstraint(['movie_id'], ['movies.id'], ),
    sa.PrimaryKeyConstraint('movie_id', 'actor_id')
    )
    op.create_table('movie_genres',
    sa.Column('movie_id', sa.Integer(), nullable=False),
    sa.Column('genre_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['genre_id'], ['genres.id'], ),
    sa.ForeignKeyConstraint(['movie_id'], ['movies.id'], ),
    sa.PrimaryKeyConstraint('movie_id', 'genre_id')
    )
    op.create_table('movie_tags',
    sa.Column('movie_id', sa.Integer(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['movie_id'], ['movies.id'], ),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
    sa.PrimaryKeyConstraint('movie_id', 'tag_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('movie_tags')
    op.drop_table('movie_genres')
    op.drop_table('movie_actors')
    op.drop_index(op.f('ix_favorites_movie_id'), table_name='favorites')
    op.drop_index(op.f('ix_favorites_id'), table_name='favorites')
    op.drop_table('favorites')
    op.drop_index(op.f('ix_movies_year'), table_name='movies')
    op.drop_index(op.f('ix_movies_title'), table_name='movies')
    op.drop_index(op.f('ix_movies_original_title'), table_name='movies')
    op.drop_index(op.f('ix_movies_id'), table_name='movies')
    op.drop_table('movies')
    op.drop_index(op.f('ix_directory_paths_id'), table_name='directory_paths')
    op.drop_table('directory_paths')
    op.drop_index(op.f('ix_v_images_uuid'), table_name='v_images')
    op.drop_index(op.f('ix_v_images_id'), table_name='v_images')
    op.drop_table('v_images')
    op.drop_index(op.f('ix_tags_name'), table_name='tags')
    op.drop_index(op.f('ix_tags_id'), table_name='tags')
    op.drop_table('tags')
    op.drop_index(op.f('ix_series_name'), table_name='series')
    op.drop_index(op.f('ix_series_id'), table_name='series')
    op.drop_table('series')
    op.drop_index(op.f('ix_mapping_rules_type'), table_name='mapping_rules')
    op.drop_index(op.f('ix_mapping_rules_status'), table_name='mapping_rules')
    op.drop_index(op.f('ix_mapping_rules_original_value'), table_name='mapping_rules')
    op.drop_index(op.f('ix_mapping_rules_id'), table_name='mapping_rules')
    op.drop_table('mapping_rules')
    op.drop_index(op.f('ix_genres_name'), table_name='genres')
    op.drop_index(op.f('ix_genres_id'), table_name='genres')
    op.drop_table('genres')
    op.drop_index(op.f('ix_directories_id'), table_name='directories')
    op.drop_table('directories')
    op.drop_index(op.f('ix_configs_id'), table_name='configs')
    op.drop_index(op.f('ix_configs_config_key'), table_name='configs')
    op.drop_table('configs')
    op.drop_index(op.f('ix_actors_name'), table_name='actors')
    op.drop_index(op.f('ix_actors_id'), table_name='actors')
    op.drop_table('actors')
    # ### end Alembic commands ###
