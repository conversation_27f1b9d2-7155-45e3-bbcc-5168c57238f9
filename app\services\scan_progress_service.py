"""
扫描进度追踪服务
提供扫描任务的进度追踪、状态管理和控制功能
"""
import asyncio
import threading
import time
from datetime import datetime
from typing import Dict, Optional, List, Any
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ScanStatus(Enum):
    """扫描状态枚举"""
    IDLE = "idle"           # 空闲
    RUNNING = "running"     # 运行中
    PAUSED = "paused"       # 暂停
    COMPLETED = "completed" # 完成
    CANCELLED = "cancelled" # 取消
    ERROR = "error"         # 错误


class ScanProgressService:
    """扫描进度追踪服务"""
    
    def __init__(self):
        self.scan_tasks: Dict[str, Dict[str, Any]] = {}  # 扫描任务字典
        self.lock = threading.Lock()
        
    def create_scan_task(self, task_id: str, directories: List[Dict], scan_type: str = "增量扫描") -> str:
        """
        创建扫描任务
        
        Args:
            task_id: 任务ID
            directories: 要扫描的目录列表
            scan_type: 扫描类型
            
        Returns:
            任务ID
        """
        with self.lock:
            self.scan_tasks[task_id] = {
                "task_id": task_id,
                "status": ScanStatus.IDLE,
                "scan_type": scan_type,
                "directories": directories,
                "total_directories": len(directories),
                "current_directory_index": 0,
                "current_directory_name": "",
                "current_file_path": "",
                "total_files": 0,
                "processed_files": 0,
                "new_movies": 0,
                "updated_movies": 0,
                "deleted_movies": 0,
                "errors": [],
                "start_time": None,
                "end_time": None,
                "pause_requested": False,
                "cancel_requested": False
            }
        
        logger.info(f"创建扫描任务: {task_id}, 目录数量: {len(directories)}")
        return task_id
    
    def start_scan_task(self, task_id: str) -> bool:
        """
        开始扫描任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功开始
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return False
            
            task = self.scan_tasks[task_id]
            if task["status"] != ScanStatus.IDLE:
                return False
            
            task["status"] = ScanStatus.RUNNING
            task["start_time"] = datetime.now()
            task["pause_requested"] = False
            task["cancel_requested"] = False
        
        logger.info(f"开始扫描任务: {task_id}")
        return True
    
    def update_scan_progress(self, task_id: str, **kwargs) -> bool:
        """
        更新扫描进度
        
        Args:
            task_id: 任务ID
            **kwargs: 更新的字段
            
        Returns:
            是否成功更新
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return False
            
            task = self.scan_tasks[task_id]
            
            # 更新字段
            for key, value in kwargs.items():
                if key in task:
                    task[key] = value
        
        return True
    
    def pause_scan_task(self, task_id: str) -> bool:
        """
        暂停扫描任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功暂停
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return False
            
            task = self.scan_tasks[task_id]
            if task["status"] == ScanStatus.RUNNING:
                task["pause_requested"] = True
                logger.info(f"请求暂停扫描任务: {task_id}")
                return True
        
        return False
    
    def resume_scan_task(self, task_id: str) -> bool:
        """
        恢复扫描任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功恢复
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return False
            
            task = self.scan_tasks[task_id]
            if task["status"] == ScanStatus.PAUSED:
                task["status"] = ScanStatus.RUNNING
                task["pause_requested"] = False
                logger.info(f"恢复扫描任务: {task_id}")
                return True
        
        return False
    
    def cancel_scan_task(self, task_id: str) -> bool:
        """
        取消扫描任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return False
            
            task = self.scan_tasks[task_id]
            if task["status"] in [ScanStatus.RUNNING, ScanStatus.PAUSED]:
                task["cancel_requested"] = True
                logger.info(f"请求取消扫描任务: {task_id}")
                return True
        
        return False
    
    def complete_scan_task(self, task_id: str, success: bool = True, error_message: str = None) -> bool:
        """
        完成扫描任务
        
        Args:
            task_id: 任务ID
            success: 是否成功完成
            error_message: 错误消息
            
        Returns:
            是否成功完成
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return False
            
            task = self.scan_tasks[task_id]
            task["end_time"] = datetime.now()
            
            if success:
                task["status"] = ScanStatus.COMPLETED
            else:
                task["status"] = ScanStatus.ERROR
                if error_message:
                    task["errors"].append(error_message)
        
        logger.info(f"完成扫描任务: {task_id}, 成功: {success}")
        return True
    
    def get_scan_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取扫描进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            扫描进度信息
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return None
            
            task = self.scan_tasks[task_id].copy()
            
            # 转换状态为字符串
            task["status"] = task["status"].value

            # 格式化时间
            if task["start_time"]:
                task["start_time"] = task["start_time"].isoformat()
            if task["end_time"]:
                task["end_time"] = task["end_time"].isoformat()

            # 调试日志
            logger.debug(f"返回扫描进度: task_id={task_id}, status={task['status']}, processed_files={task['processed_files']}, total_files={task['total_files']}")

            return task
    
    def check_pause_or_cancel(self, task_id: str) -> str:
        """
        检查是否需要暂停或取消
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作类型: 'continue', 'pause', 'cancel'
        """
        with self.lock:
            if task_id not in self.scan_tasks:
                return 'cancel'
            
            task = self.scan_tasks[task_id]
            
            if task["cancel_requested"]:
                task["status"] = ScanStatus.CANCELLED
                return 'cancel'
            
            if task["pause_requested"]:
                task["status"] = ScanStatus.PAUSED
                return 'pause'
            
            return 'continue'
    

    
    def get_all_active_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有活跃的扫描任务
        
        Returns:
            活跃任务列表
        """
        with self.lock:
            active_tasks = []
            for task_id, task in self.scan_tasks.items():
                if task["status"] in [ScanStatus.RUNNING, ScanStatus.PAUSED]:
                    task_copy = task.copy()
                    task_copy["status"] = task_copy["status"].value
                    if task_copy["start_time"]:
                        task_copy["start_time"] = task_copy["start_time"].isoformat()
                    active_tasks.append(task_copy)
            
            return active_tasks


# 全局扫描进度服务实例
scan_progress_service = ScanProgressService()
