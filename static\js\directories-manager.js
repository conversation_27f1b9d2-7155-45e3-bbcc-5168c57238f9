/**
 * 目录管理器
 * 使用DaisyUI v5组件重新构建的目录管理功能
 */
class DirectoriesManager {
    constructor() {
        this.directories = [];
        this.selectedDirectories = new Set();
        this.currentDirectory = null;
        this.searchTimeout = null;
        this.api = window.api; // 使用全局api实例
        this.directoryBrowser = null; // 目录浏览器实例
        this.currentPathIndex = null; // 当前编辑的路径索引
        this.activeScanTasks = new Map(); // 存储活跃扫描任务

        // 确保API客户端已初始化
        if (!this.api) {
            console.error('API客户端未初始化');
            return;
        }

        // 初始化扫描进度管理器（集成到当前类中）
        this.initializeScanProgressManager();

        this.init();
    }

    /**
     * 初始化目录管理器
     */
    async init() {
        // 检查是否在正确的页面上
        if (!this.isOnDirectoriesPage()) {
            return;
        }

        this.bindEvents();
        await this.loadDirectories();

        // 恢复正在进行的扫描状态
        await this.restoreActiveScanStates();
    }

    /**
     * 检查是否在目录管理页面
     */
    isOnDirectoriesPage() {
        return document.getElementById('directories-container') !== null;
    }

    /**
     * 初始化扫描进度管理器
     */
    initializeScanProgressManager() {
        // 创建一个简化的扫描进度管理器
        window.scanProgressManager = {
            startScan: async (scanFunction, ...args) => {
                try {
                    // 直接调用扫描API并返回任务ID
                    const result = await scanFunction(...args);
                    if (result.success && result.task_id) {
                        return result.task_id;
                    } else {
                        throw new Error(result.message || '启动扫描失败');
                    }
                } catch (error) {
                    console.error('启动扫描时出错:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 添加目录按钮
        document.getElementById('add-directory-btn')?.addEventListener('click', () => {
            this.showDirectoryModal();
        });

        document.getElementById('empty-add-directory-btn')?.addEventListener('click', () => {
            this.showDirectoryModal();
        });

        // 表单提交
        document.getElementById('directory-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDirectory();
        });

        // 搜索
        document.getElementById('search-input')?.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // 刷新按钮
        document.getElementById('refresh-directories-btn')?.addEventListener('click', () => {
            this.loadDirectories();
        });

        // 批量扫描
        document.getElementById('batch-scan-btn')?.addEventListener('click', () => {
            this.handleBatchScan();
        });

        // 删除确认
        document.getElementById('confirm-delete-directory-btn')?.addEventListener('click', () => {
            this.confirmDelete();
        });

        // 添加路径按钮
        document.getElementById('add-path-btn')?.addEventListener('click', () => {
            this.addNewPathInput();
        });

    }

    /**
     * 加载目录列表
     */
    async loadDirectories() {
        try {
            this.showLoading();

            const response = await this.api.getDirectories(false, true);

            if (response.success) {
                this.directories = response.data || [];

                this.renderDirectories();
                this.hideLoading();
            } else {
                throw new Error(response.message || '加载目录失败');
            }
        } catch (error) {
            console.error('Failed to load directories:', error);

            handleApiError(error, '加载目录列表');
            this.showError();
        }
    }

    /**
     * 渲染目录列表
     */
    renderDirectories(filteredDirectories = null) {
        const directories = filteredDirectories || this.directories;
        const container = document.getElementById('directories-container');
        const emptyState = document.getElementById('directories-empty-state');

        if (!container) {
            console.error('找不到目录容器元素 #directories-container');
            return;
        }

        // 隐藏加载状态
        this.hideLoading();

        if (directories.length === 0) {
            container.classList.add('hidden');
            if (emptyState) emptyState.classList.remove('hidden');
            return;
        }

        container.classList.remove('hidden');
        if (emptyState) emptyState.classList.add('hidden');

        try {
            const directoriesHtml = directories.map((directory) => {
                return this.createDirectoryCard(directory);
            }).join('');

            container.innerHTML = directoriesHtml;

            // 绑定卡片事件
            this.bindCardEvents();
        } catch (error) {
            console.error('渲染目录卡片时出错:', error);
        }
    }

    /**
     * 创建目录卡片
     */
    createDirectoryCard(directory) {
        try {
            const isSelected = this.selectedDirectories.has(directory.id);
            const statusBadgeClass = directory.enabled ? 'badge-success' : 'badge-error';
            const statusText = directory.enabled ? '启用' : '禁用';

            const cardHtml = `
                <div class="card bg-base-100 shadow-md border border-base-300 hover:shadow-lg hover:border-primary/30 transition-all duration-200 ${isSelected ? 'ring-2 ring-primary ring-opacity-50' : ''}" data-directory-id="${directory.id}">
                    <div class="card-body p-4">
                        <!-- 卡片头部：路径和选择框 -->
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center gap-2 flex-1 min-w-0">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-primary/10 text-primary rounded-lg flex items-center justify-center">
                                        <i class="bi bi-folder" aria-label="目录图标"></i>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-semibold text-base leading-tight truncate" title="${directory.name || '未命名目录'}">${directory.name || '未命名目录'}</h3>
                                    <p class="text-xs text-base-content/60 mt-1 line-clamp-1" title="${this.getDirectoryPathsDisplay(directory)}">${this.getDirectoryPathsDisplay(directory)}</p>
                                </div>
                            </div>
                            <div class="flex-shrink-0 ml-2">
                                <input type="checkbox" class="checkbox checkbox-primary checkbox-sm directory-checkbox"
                                       ${isSelected ? 'checked' : ''} data-directory-id="${directory.id}">
                            </div>
                        </div>

                        <!-- 状态和统计信息 -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <div class="badge ${statusBadgeClass} badge-sm">${statusText}</div>
                                <!-- 扫描状态徽章 -->
                                <div class="scan-status-badge hidden">
                                    <div class="badge badge-info badge-sm">
                                        <span class="loading loading-spinner loading-xs mr-1"></span>
                                        <span class="scan-status-text">扫描中</span>
                                    </div>
                                </div>
                                <div class="text-xs text-base-content/50">
                                    ${(directory.file_count || 0).toLocaleString()} 文件
                                </div>
                            </div>
                            <div class="text-xs text-base-content/50">
                                ${directory.last_scan ?
                                    utils.formatDate(directory.last_scan, 'MM-DD HH:mm') :
                                    '未扫描'
                                }
                            </div>
                        </div>

                        <!-- 扫描进度区域 -->
                        <div class="scan-progress-area hidden mb-3">
                            <!-- 进度条 -->
                            <progress class="progress progress-primary w-full h-2 mb-2" value="0" max="100"></progress>

                            <!-- 进度信息 -->
                            <div class="text-xs text-base-content/70 space-y-1">
                                <div class="flex justify-between">
                                    <span class="scan-current-file">准备中...</span>
                                    <span class="scan-percentage">0%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="scan-files-count">文件: 0/0</span>
                                    <span class="scan-movies-count">新增: 0 部影片</span>
                                </div>
                            </div>

                            <!-- 扫描控制按钮 -->
                            <div class="scan-controls flex gap-1 mt-2">
                                <button type="button" class="btn btn-xs btn-warning pause-scan-btn">
                                    <i class="bi bi-pause" style="font-size: 0.75rem;"></i>
                                    暂停
                                </button>
                                <button type="button" class="btn btn-xs btn-info resume-scan-btn hidden">
                                    <i class="bi bi-play" style="font-size: 0.75rem;"></i>
                                    恢复
                                </button>
                                <button type="button" class="btn btn-xs btn-error cancel-scan-btn">
                                    <i class="bi bi-x" style="font-size: 0.75rem;"></i>
                                    取消
                                </button>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex gap-1">
                            <button type="button" class="btn btn-xs btn-primary flex-1 edit-directory-btn"
                                    data-directory-id="${directory.id}"
                                    title="编辑目录">
                                <i class="bi bi-pencil" style="font-size: 0.75rem;" aria-label="编辑图标"></i>
                                编辑
                            </button>
                            <button type="button" class="btn btn-xs btn-success flex-1 scan-directory-btn"
                                    data-directory-id="${directory.id}" ${!directory.enabled ? 'disabled' : ''}
                                    title="增量扫描（只处理新增或修改的文件）">
                                <i class="bi bi-arrow-repeat" style="font-size: 0.75rem;" aria-label="增量扫描图标"></i>
                                增量扫描
                            </button>

                            <div class="dropdown dropdown-end">
                                <div tabindex="0" role="button" class="btn btn-xs btn-ghost">
                                    <i class="bi bi-three-dots-vertical" style="font-size: 0.75rem;" aria-label="更多操作图标"></i>
                                </div>
                                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-36 p-2 shadow-lg border border-base-300">
                                    <li>
                                        <button type="button" class="full-scan-directory-btn text-xs"
                                                data-directory-id="${directory.id}" ${!directory.enabled ? 'disabled' : ''}>
                                            <i class="bi bi-arrow-clockwise" style="font-size: 0.75rem;" aria-label="全量扫描图标"></i>
                                            全量扫描
                                        </button>
                                    </li>
                                    <li><div class="divider my-1"></div></li>
                                    <li>
                                        <button type="button" class="toggle-directory-btn text-xs"
                                                data-directory-id="${directory.id}" data-enabled="${directory.enabled}">
                                            <i class="bi bi-power" style="font-size: 0.75rem;" aria-label="启用停用图标"></i>
                                            ${directory.enabled ? '停用' : '启用'}
                                        </button>
                                    </li>
                                    <li>
                                        <button type="button" class="delete-directory-btn text-xs text-error"
                                                data-directory-id="${directory.id}">
                                            <i class="bi bi-trash" style="font-size: 0.75rem;" aria-label="删除图标"></i>
                                            删除
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return cardHtml;

        } catch (error) {
            console.error('创建目录卡片时出错:', error);

            // 返回一个简单的错误卡片
            return `
                <div class="card bg-base-100 shadow-lg border border-error">
                    <div class="card-body">
                        <h3 class="card-title text-error">卡片生成错误</h3>
                        <p>目录ID: ${directory.id}</p>
                        <p>错误: ${error.message}</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 绑定卡片事件
     */
    bindCardEvents() {
        // 绑定复选框事件
        const checkboxes = document.querySelectorAll('.directory-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                if (e.target.checked) {
                    this.selectedDirectories.add(directoryId);
                } else {
                    this.selectedDirectories.delete(directoryId);
                }
                this.updateBatchScanButton();
            });
        });

        // 绑定编辑按钮事件
        const editButtons = document.querySelectorAll('.edit-directory-btn');

        editButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.editDirectory(directoryId);
            });
        });

        // 绑定删除按钮事件
        const deleteButtons = document.querySelectorAll('.delete-directory-btn');

        deleteButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.deleteDirectory(directoryId);
            });
        });

        // 绑定增量扫描按钮事件
        const scanButtons = document.querySelectorAll('.scan-directory-btn');

        scanButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.scanDirectory(directoryId);
            });
        });

        // 绑定全量扫描按钮事件
        const fullScanButtons = document.querySelectorAll('.full-scan-directory-btn');

        fullScanButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.fullScanDirectory(directoryId);
            });
        });

        // 绑定启用/停用按钮事件
        const toggleButtons = document.querySelectorAll('.toggle-directory-btn');

        toggleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                const enabled = e.target.dataset.enabled === 'true';
                this.toggleDirectory(directoryId, !enabled);
            });
        });


    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loading = document.getElementById('directories-loading');
        const container = document.getElementById('directories-container');
        const emptyState = document.getElementById('directories-empty-state');

        if (loading) loading.classList.remove('hidden');
        if (container) container.classList.add('hidden');
        if (emptyState) emptyState.classList.add('hidden');
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loading = document.getElementById('directories-loading');
        if (loading) loading.classList.add('hidden');
    }

    /**
     * 显示错误状态
     */
    showError() {
        const loading = document.getElementById('directories-loading');
        const container = document.getElementById('directories-container');
        const emptyState = document.getElementById('directories-empty-state');

        if (loading) loading.classList.add('hidden');
        if (container) container.classList.add('hidden');
        if (emptyState) emptyState.classList.remove('hidden');
    }

    /**
     * 显示目录模态框
     */
    showDirectoryModal(directory = null) {
        this.currentDirectory = directory;
        const modal = document.getElementById('directory-modal');
        const title = document.getElementById('directory-modal-title');
        const form = document.getElementById('directory-form');

        if (!modal || !title || !form) {
            return;
        }

        // 设置标题
        title.textContent = directory ? '编辑目录' : '添加目录';

        // 重置表单
        form.reset();
        this.clearFormErrors();

        // 如果是编辑模式，填充表单数据
        if (directory) {
            document.getElementById('directory-name').value = directory.name || '';
            document.getElementById('directory-enabled').checked = directory.enabled !== false;
        }

        // 初始化路径管理
        this.initializePathsManagement();

        // 加载路径数据
        this.loadDirectoryPathsForEdit(directory ? directory.id : null);

        // 显示模态框
        modal.showModal();
    }

    /**
     * 保存目录
     */
    async saveDirectory() {
        const form = document.getElementById('directory-form');
        const submitBtn = document.getElementById('directory-submit-btn');
        const spinner = submitBtn.querySelector('.loading');

        if (!form || !submitBtn) {
            return;
        }

        const formData = new FormData(form);

        // 清除之前的错误
        this.clearFormErrors();

        // 验证表单和路径
        if (!this.validateDirectoryForm(formData)) {
            return;
        }

        try {
            submitBtn.disabled = true;
            if (spinner) spinner.classList.remove('hidden');

            const name = formData.get('name');
            const enabled = formData.has('enabled');
            const paths = this.getPathsFromForm();

            const directoryData = {
                name: name,
                enabled: enabled,
                paths: paths
            };

            let response;
            if (this.currentDirectory) {
                // 更新现有目录
                response = await this.api.updateDirectoryWithPaths(this.currentDirectory.id, directoryData);
            } else {
                // 添加新目录
                response = await this.api.createDirectoryWithPaths(directoryData);
            }

            if (response.success) {
                // 显示成功消息
                if (window.toast) {
                    window.toast.success(response.message || '目录保存成功');
                }

                // 关闭模态框
                document.getElementById('directory-modal').close();

                // 重新加载目录列表
                this.loadDirectories();
            } else {
                throw new Error(response.message || '保存失败');
            }

        } catch (error) {
            console.error('保存目录时出错:', error);
            handleApiError(error, '保存目录');
        } finally {
            submitBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 验证目录表单（包含多路径）
     */
    validateDirectoryForm(formData) {
        let isValid = true;
        const name = formData.get('name');

        // 验证目录名称
        if (!name || name.trim() === '') {
            this.showFieldError('directory-name', '请输入目录名称');
            isValid = false;
        } else if (name.trim().length < 1 || name.trim().length > 100) {
            this.showFieldError('directory-name', '目录名称长度必须在1-100字符之间');
            isValid = false;
        } else if (!/^[\w\s\u4e00-\u9fff-]+$/.test(name.trim())) {
            this.showFieldError('directory-name', '目录名称只能包含中文、英文、数字、空格、下划线和连字符');
            isValid = false;
        }

        // 验证路径
        const pathValidation = this.validatePaths();
        if (!pathValidation.valid) {
            this.showFieldError('directory-paths', pathValidation.message);
            isValid = false;
        }

        return isValid;
    }

    /**
     * 显示字段错误
     */
    showFieldError(fieldId, message) {
        const errorElement = document.getElementById(`${fieldId}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }
    }

    /**
     * 清除表单错误
     */
    clearFormErrors() {
        const errorElements = document.querySelectorAll('[id$="-error"]');
        errorElements.forEach(element => {
            element.textContent = '';
            element.classList.add('hidden');
        });
    }

    /**
     * 编辑目录
     */
    editDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (directory) {
            this.showDirectoryModal(directory);
        }
    }

    /**
     * 删除目录
     */
    deleteDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        // 设置删除确认信息
        const messageElement = document.getElementById('delete-directory-message');
        const warningElement = document.getElementById('delete-directory-warning');
        const movieCountElement = document.getElementById('directory-movie-count');

        if (messageElement) {
            messageElement.textContent = `您确定要删除目录 "${directory.name}" 吗？`;
        }

        if (directory.file_count > 0) {
            if (warningElement) warningElement.classList.remove('hidden');
            if (movieCountElement) movieCountElement.textContent = directory.file_count;
        } else {
            if (warningElement) warningElement.classList.add('hidden');
        }

        // 保存要删除的目录ID
        this.directoryToDelete = directoryId;

        // 显示删除确认模态框
        const modal = document.getElementById('delete-directory-modal');
        if (modal) {
            modal.showModal();
        }
    }

    /**
     * 确认删除目录
     */
    async confirmDelete() {
        if (!this.directoryToDelete) {
            return;
        }

        const confirmBtn = document.getElementById('confirm-delete-directory-btn');
        const spinner = confirmBtn?.querySelector('.loading');

        try {
            if (confirmBtn) confirmBtn.disabled = true;
            if (spinner) spinner.classList.remove('hidden');

            const result = await this.api.deleteDirectory(this.directoryToDelete);

            if (result.success) {
                // 显示成功消息
                if (window.toast) {
                    window.toast.success(result.message || '目录删除成功');
                }

                // 关闭模态框
                document.getElementById('delete-directory-modal').close();

                // 重新加载目录列表
                this.loadDirectories();
            } else {
                throw new Error(result.message || '删除失败');
            }

        } catch (error) {
            console.error('删除目录时出错:', error);
            handleApiError(error, '删除目录');
        } finally {
            if (confirmBtn) confirmBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
            this.directoryToDelete = null;
        }
    }

    /**
     * 增量扫描目录
     */
    async scanDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        if (!directory.enabled) {
            if (window.toast) {
                window.toast.warning('目录已禁用，无法扫描');
            }
            return;
        }

        try {
            // 使用扫描进度管理器启动扫描
            const taskId = await window.scanProgressManager.startScan(
                this.api.scanDirectory.bind(this.api),
                directoryId
            );

            // 如果有任务ID，显示进度并监听完成事件
            if (taskId) {
                // 显示进度
                this.showDirectoryCardProgress([directoryId], taskId);

                // 监听扫描完成事件
                this.monitorScanCompletion(taskId, [directoryId]);
            }

        } catch (error) {
            console.error('启动增量扫描任务时出错:', error);
            handleApiError(error, '启动增量扫描任务');
        }
    }

    /**
     * 全量扫描目录
     */
    async fullScanDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        if (!directory.enabled) {
            if (window.toast) {
                window.toast.warning('目录已禁用，无法扫描');
            }
            return;
        }

        try {
            // 使用扫描进度管理器启动扫描
            const taskId = await window.scanProgressManager.startScan(
                this.api.fullScanDirectory.bind(this.api),
                directoryId
            );

            // 如果有任务ID，显示进度并监听完成事件
            if (taskId) {
                // 显示进度
                this.showDirectoryCardProgress([directoryId], taskId);

                // 监听扫描完成事件
                this.monitorScanCompletion(taskId, [directoryId]);
            }

        } catch (error) {
            console.error('启动全量扫描任务时出错:', error);
            handleApiError(error, '启动全量扫描任务');
        }
    }

    /**
     * 切换目录启用状态
     */
    async toggleDirectory(directoryId, enabled) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        try {
            const updateData = {
                name: directory.name,
                enabled: enabled
            };

            const result = await this.api.updateDirectory(directoryId, updateData);

            if (result.success) {
                // 显示成功消息
                if (window.toast) {
                    window.toast.success(`目录已${enabled ? '启用' : '停用'}`);
                }

                // 重新加载目录列表
                this.loadDirectories();
            } else {
                throw new Error(result.message || '操作失败');
            }

        } catch (error) {
            console.error('切换目录状态时出错:', error);
            handleApiError(error, '切换目录状态');
        }
    }

    /**
     * 处理搜索
     */
    handleSearch(query) {
        // 清除之前的搜索定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 设置新的搜索定时器
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    /**
     * 执行搜索
     */
    performSearch(query) {
        // 立即清空目录列表内容并显示加载状态
        this.clearDirectoryContent();
        this.showLoading();

        // 使用 setTimeout 确保UI更新后再进行搜索
        setTimeout(() => {
            try {
                if (!query || query.trim() === '') {
                    this.renderDirectories();
                    return;
                }

                const searchTerm = query.toLowerCase().trim();
                const filteredDirectories = this.directories.filter(directory => {
                    return (
                        (directory.name && directory.name.toLowerCase().includes(searchTerm)) ||
                        (directory.paths && directory.paths.some(path => path.path.toLowerCase().includes(searchTerm))) ||
                        (directory.description && directory.description.toLowerCase().includes(searchTerm))
                    );
                });

                this.renderDirectories(filteredDirectories);
            } catch (error) {
                console.error('搜索过程中发生错误:', error);
                this.showErrorState('搜索失败，请重试');
            }
        }, 50);
    }

    /**
     * 更新批量扫描按钮状态
     */
    updateBatchScanButton() {
        const batchScanBtn = document.getElementById('batch-scan-btn');
        if (!batchScanBtn) return;

        if (this.selectedDirectories.size > 0) {
            batchScanBtn.classList.remove('hidden');
        } else {
            batchScanBtn.classList.add('hidden');
        }
    }

    /**
     * 处理批量扫描
     */
    async handleBatchScan() {
        if (this.selectedDirectories.size === 0) {
            if (window.toast) {
                window.toast.warning('请先选择要扫描的目录');
            }
            return;
        }

        const selectedIds = Array.from(this.selectedDirectories);
        const enabledDirectories = selectedIds.filter(id => {
            const directory = this.directories.find(d => d.id === id);
            return directory && directory.enabled;
        });

        if (enabledDirectories.length === 0) {
            if (window.toast) {
                window.toast.warning('选中的目录都已禁用，无法扫描');
            }
            return;
        }

        try {
            // 使用扫描进度管理器启动批量扫描
            const taskId = await window.scanProgressManager.startScan(
                this.api.scanDirectories.bind(this.api),
                enabledDirectories,
                false // 全量扫描
            );

            // 如果有任务ID，显示进度并监听完成事件
            if (taskId) {
                // 显示进度
                this.showDirectoryCardProgress(enabledDirectories, taskId);

                // 清除选择状态
                this.selectedDirectories.clear();
                this.updateBatchScanButton();

                // 监听扫描完成事件
                this.monitorScanCompletion(taskId, enabledDirectories);
            }

        } catch (error) {
            console.error('启动批量扫描任务时出错:', error);
            handleApiError(error, '启动批量扫描任务');
        }
    }

    /**
     * 监控扫描完成状态
     */
    async monitorScanCompletion(taskId, directoryIds = []) {
        // 存储任务ID和相关目录ID的映射
        if (!this.activeScanTasks) {
            this.activeScanTasks = new Map();
        }
        this.activeScanTasks.set(taskId, directoryIds);

        const checkInterval = setInterval(async () => {
            try {
                const result = await this.api.getScanProgress(taskId);
                if (result.success && result.progress) {
                    const progress = result.progress;
                    const status = progress.status;

                    // 更新目录卡片的进度显示
                    this.updateDirectoryCardProgress(directoryIds, progress);

                    // 检查是否完成
                    if (['completed', 'cancelled', 'error'].includes(status)) {
                        clearInterval(checkInterval);
                        this.activeScanTasks.delete(taskId);

                        // 隐藏进度显示
                        this.hideDirectoryCardProgress(directoryIds);

                        // 重新加载目录列表以更新统计信息
                        this.loadDirectories();

                        // 显示完成提示
                        if (status === 'completed' && window.toast) {
                            const newMovies = progress.new_movies || 0;
                            const updatedMovies = progress.updated_movies || 0;
                            const deletedMovies = progress.deleted_movies || 0;
                            const processedFiles = progress.processed_files || 0;

                            // 构建结果消息
                            let resultParts = [`处理了 ${processedFiles} 个文件`];
                            if (newMovies > 0) resultParts.push(`新增 ${newMovies} 部影片`);
                            if (updatedMovies > 0) resultParts.push(`更新 ${updatedMovies} 部影片`);
                            if (deletedMovies > 0) resultParts.push(`删除 ${deletedMovies} 部影片`);

                            window.toast.success(`扫描完成: ${resultParts.join('，')}`);
                        } else if (status === 'error' && window.toast) {
                            window.toast.error(`扫描失败: ${progress.error_message || '未知错误'}`);
                        } else if (status === 'cancelled' && window.toast) {
                            window.toast.warning('扫描已取消');
                        }
                    }
                }
            } catch (error) {
                // 任务可能已经不存在，停止监控
                clearInterval(checkInterval);
                this.activeScanTasks.delete(taskId);
                this.hideDirectoryCardProgress(directoryIds);
                console.warn('监控扫描完成状态时出错:', error);
            }
        }, 2000); // 每2秒检查一次

        // 设置最大监控时间（30分钟）
        setTimeout(() => {
            clearInterval(checkInterval);
            this.activeScanTasks.delete(taskId);
            this.hideDirectoryCardProgress(directoryIds);
        }, 30 * 60 * 1000);
    }

    /**
     * 显示目录卡片的扫描进度
     */
    showDirectoryCardProgress(directoryIds, taskId) {
        directoryIds.forEach(directoryId => {
            const card = document.querySelector(`[data-directory-id="${directoryId}"]`);
            if (!card) return;

            // 添加扫描中的边框样式
            card.classList.add('border-info', 'border-2');
            card.classList.remove('border-base-300');

            // 显示扫描状态徽章
            const statusBadge = card.querySelector('.scan-status-badge');
            if (statusBadge) {
                statusBadge.classList.remove('hidden');
            }

            // 显示进度区域
            const progressArea = card.querySelector('.scan-progress-area');
            if (progressArea) {
                progressArea.classList.remove('hidden');

                // 绑定控制按钮事件
                const pauseBtn = progressArea.querySelector('.pause-scan-btn');
                const resumeBtn = progressArea.querySelector('.resume-scan-btn');
                const cancelBtn = progressArea.querySelector('.cancel-scan-btn');

                if (pauseBtn) {
                    pauseBtn.onclick = () => this.pauseScanTask(taskId);
                }
                if (resumeBtn) {
                    resumeBtn.onclick = () => this.resumeScanTask(taskId);
                }
                if (cancelBtn) {
                    cancelBtn.onclick = () => this.cancelScanTask(taskId);
                }
            }

            // 禁用扫描按钮
            const scanBtns = card.querySelectorAll('.scan-directory-btn, .full-scan-directory-btn');
            scanBtns.forEach(btn => {
                btn.disabled = true;
                btn.classList.add('btn-disabled');
            });
        });
    }

    /**
     * 更新目录卡片的扫描进度
     */
    updateDirectoryCardProgress(directoryIds, progress) {
        directoryIds.forEach(directoryId => {
            const card = document.querySelector(`[data-directory-id="${directoryId}"]`);
            if (!card) return;

            const progressArea = card.querySelector('.scan-progress-area');
            if (!progressArea) return;

            // 更新进度条 - 前端计算进度百分比
            const progressBar = progressArea.querySelector('progress.progress');
            const percentageText = progressArea.querySelector('.scan-percentage');

            let percentage = 0;
            if (progress.total_files > 0) {
                percentage = Math.round((progress.processed_files / progress.total_files) * 100);
            }

            if (progressBar) {
                progressBar.value = percentage;
            }
            if (percentageText) {
                percentageText.textContent = `${percentage}%`;
            }

            // 更新当前文件
            const currentFileText = progressArea.querySelector('.scan-current-file');
            if (currentFileText) {
                // 根据状态显示不同的文本
                let displayText = '准备中...';
                if (progress.status === 'running' && progress.current_file_path) {
                    const currentFile = progress.current_file_path;
                    const fileName = currentFile.split(/[\\\/]/).pop() || currentFile;
                    const maxLength = 30;
                    displayText = fileName.length > maxLength ?
                        '...' + fileName.slice(-maxLength) : fileName;
                    currentFileText.title = currentFile;
                } else if (progress.status === 'running') {
                    displayText = '扫描中...';
                }
                currentFileText.textContent = displayText;
            }

            // 更新文件计数
            const filesCountText = progressArea.querySelector('.scan-files-count');
            if (filesCountText) {
                const processed = progress.processed_files || 0;
                const total = progress.total_files || 0;
                filesCountText.textContent = `文件: ${processed}/${total}`;
            }

            // 更新影片计数
            const moviesCountText = progressArea.querySelector('.scan-movies-count');
            if (moviesCountText) {
                const newMovies = progress.new_movies || 0;
                moviesCountText.textContent = `新增: ${newMovies} 部影片`;
            }

            // 更新状态徽章
            const statusBadge = card.querySelector('.scan-status-badge');
            const statusText = statusBadge?.querySelector('.scan-status-text');
            const pauseBtn = progressArea.querySelector('.pause-scan-btn');
            const resumeBtn = progressArea.querySelector('.resume-scan-btn');

            if (statusText && statusBadge) {
                const status = progress.status;

                // 移除所有状态类
                statusBadge.querySelector('.badge').classList.remove('badge-info', 'badge-warning', 'badge-success', 'badge-error');

                switch (status) {
                    case 'running':
                        statusText.textContent = '扫描中';
                        statusBadge.querySelector('.badge').classList.add('badge-info');
                        card.classList.remove('border-warning', 'border-success', 'border-error');
                        card.classList.add('border-info');
                        if (pauseBtn) pauseBtn.classList.remove('hidden');
                        if (resumeBtn) resumeBtn.classList.add('hidden');
                        break;
                    case 'paused':
                        statusText.textContent = '已暂停';
                        statusBadge.querySelector('.badge').classList.add('badge-warning');
                        card.classList.remove('border-info', 'border-success', 'border-error');
                        card.classList.add('border-warning');
                        if (pauseBtn) pauseBtn.classList.add('hidden');
                        if (resumeBtn) resumeBtn.classList.remove('hidden');
                        break;
                    case 'completed':
                        statusText.textContent = '已完成';
                        statusBadge.querySelector('.badge').classList.add('badge-success');
                        card.classList.remove('border-info', 'border-warning', 'border-error');
                        card.classList.add('border-success');
                        break;
                    case 'error':
                        statusText.textContent = '扫描失败';
                        statusBadge.querySelector('.badge').classList.add('badge-error');
                        card.classList.remove('border-info', 'border-warning', 'border-success');
                        card.classList.add('border-error');
                        break;
                }
            }
        });
    }

    /**
     * 隐藏目录卡片的扫描进度
     */
    hideDirectoryCardProgress(directoryIds) {
        directoryIds.forEach(directoryId => {
            const card = document.querySelector(`[data-directory-id="${directoryId}"]`);
            if (!card) return;

            // 恢复默认边框样式
            card.classList.remove('border-info', 'border-warning', 'border-success', 'border-error', 'border-2');
            card.classList.add('border-base-300');

            // 隐藏扫描状态徽章
            const statusBadge = card.querySelector('.scan-status-badge');
            if (statusBadge) {
                statusBadge.classList.add('hidden');
            }

            // 隐藏进度区域
            const progressArea = card.querySelector('.scan-progress-area');
            if (progressArea) {
                progressArea.classList.add('hidden');
            }

            // 启用扫描按钮
            const scanBtns = card.querySelectorAll('.scan-directory-btn, .full-scan-directory-btn');
            scanBtns.forEach(btn => {
                btn.disabled = false;
                btn.classList.remove('btn-disabled');
            });
        });
    }

    /**
     * 暂停扫描任务
     */
    async pauseScanTask(taskId) {
        try {
            const result = await this.api.pauseScan(taskId);
            if (result.success) {
                if (window.toast) {
                    window.toast.info('扫描已暂停');
                }
            }
        } catch (error) {
            console.error('暂停扫描任务时出错:', error);
            handleApiError(error, '暂停扫描任务');
        }
    }

    /**
     * 恢复扫描任务
     */
    async resumeScanTask(taskId) {
        try {
            const result = await this.api.resumeScan(taskId);
            if (result.success) {
                if (window.toast) {
                    window.toast.info('扫描已恢复');
                }
            }
        } catch (error) {
            console.error('恢复扫描任务时出错:', error);
            handleApiError(error, '恢复扫描任务');
        }
    }

    /**
     * 取消扫描任务
     */
    async cancelScanTask(taskId) {
        try {
            const result = await this.api.cancelScan(taskId);
            if (result.success) {
                if (window.toast) {
                    window.toast.warning('扫描已取消');
                }
            }
        } catch (error) {
            console.error('取消扫描任务时出错:', error);
            handleApiError(error, '取消扫描任务');
        }
    }

    /**
     * 恢复正在进行的扫描状态
     */
    async restoreActiveScanStates() {
        try {
            // 获取所有活跃的扫描任务
            const result = await this.api.getActiveScans();
            if (result.success && result.active_scans && result.active_scans.length > 0) {

                // 初始化活跃任务映射
                if (!this.activeScanTasks) {
                    this.activeScanTasks = new Map();
                }

                // 恢复每个活跃任务的显示和监控
                for (const scan of result.active_scans) {
                    const taskId = scan.task_id;
                    const directoryIds = scan.directory_ids || [];

                    // 存储任务映射
                    this.activeScanTasks.set(taskId, directoryIds);

                    // 显示进度
                    this.showDirectoryCardProgress(directoryIds, taskId);

                    // 开始监控
                    this.monitorScanCompletion(taskId, directoryIds);
                }

                console.log(`恢复了 ${result.active_scans.length} 个活跃扫描任务`);
            }
        } catch (error) {
            console.warn('恢复扫描状态时出错:', error);
            // 不显示错误提示，因为这不是关键功能
        }
    }

    // ==================== 多路径管理方法 ====================

    /**
     * 初始化路径管理
     */
    initializePathsManagement() {
        this.directoryPaths = []; // 当前编辑目录的路径列表
        this.pathIdCounter = 0; // 用于生成临时ID
    }

    /**
     * 加载目录的路径数据到编辑表单
     */
    async loadDirectoryPathsForEdit(directoryId) {
        if (!directoryId) {
            // 新建目录，添加一个空路径输入
            this.directoryPaths = [];
            this.addNewPathInput();
            return;
        }

        try {
            const result = await this.api.getDirectoryInfo(directoryId);

            if (result.success) {
                this.directoryPaths = result.directory.paths || [];
                this.renderPathsInEditForm();

                // 如果没有路径，添加一个空输入
                if (this.directoryPaths.length === 0) {
                    this.addNewPathInput();
                }
            } else {
                throw new Error(result.message || '加载路径失败');
            }

        } catch (error) {
            console.error('加载目录路径时出错:', error);
            // 出错时也添加一个空输入
            this.directoryPaths = [];
            this.addNewPathInput();
        }
    }

    /**
     * 在编辑表单中渲染路径列表
     */
    renderPathsInEditForm() {
        const container = document.getElementById('directory-paths-container');
        if (!container) return;

        container.innerHTML = '';

        this.directoryPaths.forEach((path, index) => {
            const pathElement = this.createPathInputElement(path, index);
            container.appendChild(pathElement);
        });
    }

    /**
     * 创建路径输入元素
     */
    createPathInputElement(path, index) {
        const div = document.createElement('div');
        div.className = 'card bg-base-100 border border-base-300';
        div.dataset.pathIndex = index;

        const pathValue = path.path || '';
        const isEnabled = path.enabled !== false;
        const isExisting = !!path.id;

        div.innerHTML = `
            <div class="card-body p-3">
                <div class="flex items-center gap-2">
                    <div class="flex-1">
                        <div class="join w-full">
                            <input type="text" class="input input-bordered input-sm join-item flex-1 path-input"
                                   value="${pathValue}" placeholder="输入目录路径" data-path-index="${index}">
                            <button type="button" class="btn btn-outline btn-sm join-item browse-path-btn"
                                    data-path-index="${index}">
                                <i class="bi bi-folder" aria-label="浏览图标"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <label class="label cursor-pointer gap-1">
                            <input type="checkbox" class="checkbox checkbox-sm path-enabled-checkbox"
                                   ${isEnabled ? 'checked' : ''} data-path-index="${index}">
                            <span class="label-text text-xs">启用</span>
                        </label>
                        ${this.directoryPaths.length > 1 ? `
                            <button type="button" class="btn btn-ghost btn-sm text-error remove-path-btn"
                                    data-path-index="${index}" title="删除路径">
                                <i class="bi bi-trash" aria-label="删除图标"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
                ${isExisting ? `<input type="hidden" class="path-id" value="${path.id}" data-path-index="${index}">` : ''}
            </div>
        `;

        // 绑定事件
        this.bindPathInputEvents(div, index);

        return div;
    }

    /**
     * 绑定路径输入元素的事件
     */
    bindPathInputEvents(element, index) {
        // 路径输入变化事件
        const pathInput = element.querySelector('.path-input');
        if (pathInput) {
            pathInput.addEventListener('input', (e) => {
                this.updatePathData(index, 'path', e.target.value);
            });
        }

        // 启用状态变化事件
        const enabledCheckbox = element.querySelector('.path-enabled-checkbox');
        if (enabledCheckbox) {
            enabledCheckbox.addEventListener('change', (e) => {
                this.updatePathData(index, 'enabled', e.target.checked);
            });
        }

        // 浏览按钮事件
        const browseBtn = element.querySelector('.browse-path-btn');
        if (browseBtn) {
            browseBtn.addEventListener('click', () => {
                this.showDirectoryBrowserForPath(index);
            });
        }

        // 删除按钮事件
        const removeBtn = element.querySelector('.remove-path-btn');
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                this.removePathInput(index);
            });
        }
    }

    /**
     * 添加新的路径输入
     */
    addNewPathInput() {
        const newPath = {
            path: '',
            enabled: true,
            isNew: true
        };

        this.directoryPaths.push(newPath);
        this.renderPathsInEditForm();
    }

    /**
     * 删除路径输入
     */
    removePathInput(index) {
        if (this.directoryPaths.length <= 1) {
            if (window.toast) {
                window.toast.warning('至少需要保留一个路径');
            }
            return;
        }

        this.directoryPaths.splice(index, 1);
        this.renderPathsInEditForm();
    }

    /**
     * 更新路径数据
     */
    updatePathData(index, field, value) {
        if (this.directoryPaths[index]) {
            this.directoryPaths[index][field] = value;
        }
    }

    /**
     * 为指定路径显示目录浏览器
     */
    showDirectoryBrowserForPath(pathIndex) {
        this.currentPathIndex = pathIndex;
        this.showDirectoryBrowser();
    }

    /**
     * 显示目录浏览器
     */
    showDirectoryBrowser() {
        // 使用全局的目录浏览器实例
        if (window.directoryBrowser) {
            // 设置路径选择回调
            window.directoryBrowser.onPathSelected = (selectedPath) => {
                this.onPathSelected(selectedPath);
            };

            // 打开目录浏览器
            window.directoryBrowser.openBrowser();
        } else {
            console.error('目录浏览器未初始化');
            if (window.toast) {
                window.toast.error('目录浏览器未初始化');
            }
        }
    }

    /**
     * 处理路径选择
     */
    onPathSelected(selectedPath) {
        if (this.currentPathIndex !== null) {
            // 更新指定索引的路径输入框
            const pathInputs = document.querySelectorAll('.path-input');
            if (pathInputs[this.currentPathIndex]) {
                pathInputs[this.currentPathIndex].value = selectedPath;

                // 触发输入事件以更新验证状态
                pathInputs[this.currentPathIndex].dispatchEvent(new Event('input'));
            }
        }

        // 重置当前路径索引
        this.currentPathIndex = null;
    }

    /**
     * 获取表单中的所有路径数据
     */
    getPathsFromForm() {
        const paths = [];

        this.directoryPaths.forEach((path) => {
            if (path.path && path.path.trim()) {
                paths.push({
                    id: path.id || null,
                    path: path.path.trim(),
                    enabled: path.enabled !== false,
                    isNew: !path.id
                });
            }
        });

        return paths;
    }

    /**
     * 验证路径数据
     */
    validatePaths() {
        const paths = this.getPathsFromForm();

        if (paths.length === 0) {
            return { valid: false, message: '至少需要一个有效路径' };
        }

        // 检查重复路径
        const pathSet = new Set();
        for (const path of paths) {
            const normalizedPath = path.path.toLowerCase();
            if (pathSet.has(normalizedPath)) {
                return { valid: false, message: `路径重复: ${path.path}` };
            }
            pathSet.add(normalizedPath);
        }

        return { valid: true };
    }

    /**
     * 获取目录路径的显示文本
     * @param {Object} directory - 目录对象
     * @returns {string} 路径显示文本
     */
    getDirectoryPathsDisplay(directory) {
        if (!directory.paths || directory.paths.length === 0) {
            return '未配置路径';
        }

        // 统一显示路径数量格式
        return `${directory.paths.length}个路径`;
    }

    /**
     * 清空目录列表内容
     */
    clearDirectoryContent() {
        const container = document.getElementById('directories-container');
        if (container) {
            container.innerHTML = '';
        }
    }

    /**
     * 显示错误状态
     */
    showErrorState(message = '加载失败，请重试') {
        const container = document.getElementById('directories-container');
        const loading = document.getElementById('directories-loading');
        const emptyState = document.getElementById('directories-empty-state');

        if (loading) {
            loading.classList.add('hidden');
        }
        if (emptyState) {
            emptyState.classList.add('hidden');
        }
        if (container) {
            container.innerHTML = `
                <div class="flex flex-col items-center justify-center py-16">
                    <div class="bg-error/10 rounded-full p-6 mb-6">
                        <i class="bi bi-exclamation-triangle text-error" style="font-size: 4rem;" aria-label="错误图标"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-2 text-base-content">加载失败</h3>
                    <p class="text-base-content/60 mb-6 text-center max-w-md">${message}</p>
                    <button type="button" class="btn btn-primary btn-wide" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise mr-2" aria-label="重试图标"></i>
                        重新加载
                    </button>
                </div>
            `;
        }
    }
}

// 页面加载完成后初始化目录管理器
document.addEventListener('DOMContentLoaded', () => {
    new DirectoriesManager();
});
