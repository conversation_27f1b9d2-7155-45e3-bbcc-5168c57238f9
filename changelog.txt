## v0.9.34 - 2025-08-01 19:00:00
- Changed: 为所有下拉菜单（dropdown）组件添加半透明背景和模糊效果样式
- Changed: 为所有模态框（modal）组件添加半透明背景和模糊效果样式
- Changed: 所有 dropdown-content 元素现在使用 `bg-base-100/80 backdrop-blur-sm` 样式类
- Changed: 所有 modal 和 modal-box 元素现在使用 `bg-base-100/80 backdrop-blur-sm` 样式类
- Improved: 增强了用户界面的视觉层次感和现代感
- Improved: 半透明背景提供更好的内容可读性，同时保持背景可见性
- Improved: 背景模糊效果提升了界面的专业外观和用户体验

## v0.9.33 - 2025-08-01 18:30:00
- Changed: 简化影片库页面和收藏页面的分页器组件，移除页码数字显示
- Changed: 分页器现在只显示"上一页"和"下一页"按钮，保持简洁的界面设计
- Changed: 优化分页信息显示格式，现在显示"第 X 页，共 Y 页"以及详细的记录范围
- Improved: 保持分页器的所有功能（翻页、状态管理、禁用状态）不变
- Improved: 在第一页时"上一页"按钮自动禁用，在最后一页时"下一页"按钮自动禁用
- Improved: 分页器保持响应式设计和 DaisyUI 样式一致性

## v0.9.32 - 2025-08-01 18:00:00
- Removed: 完全清理前端代码中废弃的 `{num}` 路径参数相关代码
- Removed: 删除 `static/js/movie-detail.js` 中的 `loadMovieDetailByNum()` 和 `getMovieIdByNum()` 方法
- Removed: 删除 `static/js/movie-edit.js` 中的 `getMovieIdByNum()` 方法和 num 参数处理逻辑
- Changed: 简化 `movie-detail.js` 和 `movie-edit.js` 的初始化函数，移除 num 参数
- Changed: 更新 `templates/movie_detail.html` 和 `templates/movie_edit.html` 模板，移除 num 变量传递
- Fixed: 修复前端代码中所有与废弃 num 路径相关的引用和逻辑
- Improved: 前端代码结构更加简洁，移除了不再使用的代码路径

## v0.9.31 - 2025-08-01 17:00:00
- Fixed: 修复仪表板统计信息显示问题，总影片数和今日添加数量现在正确显示
- Changed: 调整媒体目录卡片设计，目录名称重叠在图片底部，影片数量使用badge显示
- Changed: 进一步缩小媒体目录图片尺寸（从w-56改为w-40），让一页显示更多目录
- Fixed: 修复统计数据字段映射错误（movies_count -> total_movies）
- Improved: 优化今日添加影片的计算逻辑，增加错误处理和时间范围验证

## v0.9.30 - 2025-08-01 16:45:00
- Added: 完整的仪表板页面实现，包含最新影片轮播、媒体目录展示和最近添加影片
- Added: 仪表板轮播组件，支持自动播放、手动导航和响应式设计
- Added: 媒体目录网格展示，显示目录封面图片和影片数量统计
- Added: 最近添加影片横向滚动展示，支持鼠标滚轮和触摸操作
- Added: 仪表板统计信息卡片，显示总影片数、目录数、收藏数和今日添加数
- Added: 仪表板刷新功能，支持一键更新所有数据
- Added: dashboard.js JavaScript模块，处理所有仪表板交互逻辑
- Added: 使用现有API端点获取数据，避免创建新的专用API
- Fixed: 修复Toast通知引用错误，统一使用window.toast对象
- Fixed: 修复图片加载问题，正确处理目录封面图片路径
- Improved: 使用DaisyUI v5组件实现响应式布局，无需自定义CSS
- Improved: 轮播图支持键盘导航和触摸手势操作
- Improved: 所有组件支持跨平台兼容性（Windows、Linux、macOS）

## v0.9.29 - 2025-07-31 22:30:00
- Changed: 电影API端点重构，统一命名规范
- Changed: POST /api/movies → POST /api/movies/list（电影列表查询）
- Changed: PUT /api/movies/{movie_id} → PUT /api/movies/edit/{movie_id}（电影信息更新）
- Changed: DELETE /api/movies/{movie_id} → DELETE /api/movies/（批量删除，支持JSON请求体）
- Added: MovieDeleteRequest请求模型，支持movies_ids数组和force标志
- Added: MovieService.batch_delete_movies方法，支持批量删除电影
- Fixed: 前端API客户端更新，支持新的电影API端点
- Fixed: movie-edit.js中使用统一API客户端替代直接fetch调用
- Improved: 电影API与其他模块（tags、genres、series、actors）保持一致的命名规范

## v0.9.28 - 2025-07-31 21:15:00
- Fixed: 全面统一前端API调用，将所有直接fetch调用替换为api.js封装方法
- Fixed: 修复advanced-search.js中tags、genres、series的直接API调用
- Fixed: 修复movie-edit.js中movies、series、genres、tags、actors的直接API调用
- Fixed: 修复movie-detail.js中movies和nfo-status的直接API调用
- Fixed: 修复directory-browser.js中目录浏览的直接API调用
- Added: 为api.js添加getNFOStatus、syncMovieNFO、browseDirectory等缺失的API方法
- Improved: 前端API调用完全统一管理，提高代码维护性和一致性
- Improved: 统一错误处理和响应数据访问方式，避免API端点分散问题

## v0.9.27 - 2025-07-31 20:45:00
- Fixed: 修复前端目录管理器中残留的旧API调用，统一使用api.js封装的方法
- Fixed: 修复directories-manager.js中直接使用fetch调用旧API路径的问题
- Fixed: 统一目录相关操作的错误处理和响应数据访问方式
- Improved: 前端API调用完全统一到api.js中，提高代码维护性

## v0.9.26 - 2025-07-31 19:00:00
- Changed: 目录扫描接口整合，统一为单一扫描接口
- Changed: 扫描接口路径整合：POST /api/directories/{directory_id}/scan、POST /api/directories/scan-all、POST /api/directories/scan → POST /api/directories/scan
- Added: DirectoryScanRequest 请求模型，支持指定目录列表和扫描模式
- Added: 统一扫描接口支持两种模式：指定目录扫描（提供directories_ids）和全部目录扫描（空directories_ids）
- Improved: 扫描接口逻辑优化，增强错误处理和日志记录
- Updated: 前端API客户端新增 scanDirectories 和 scanAllDirectories 方法，保留原有方法的兼容性
- Updated: API文档更新，反映目录扫描接口的整合变更

## v0.9.25 - 2025-07-31 18:30:00
- Changed: 目录删除接口重构，支持批量删除功能
- Changed: 目录删除接口路径调整：DELETE /api/directories/{directory_id} → DELETE /api/directories
- Added: DirectoryDeleteRequest 请求模型，支持批量删除多个目录
- Added: 批量删除统计信息，包含成功/失败数量和详细结果
- Improved: 删除操作错误处理，单个目录删除失败不影响其他目录
- Updated: 前端API客户端新增 deleteDirectories 批量删除方法，保留 deleteDirectory 兼容性方法
- Updated: API文档更新，反映目录删除接口的变更

## v0.9.24 - 2025-07-31 18:15:00
- Changed: 目录列表接口路径进一步调整：POST /api/directories → POST /api/directories/list
- Improved: 目录管理API路径结构更加清晰，与其他模块保持一致
- Updated: 前端API客户端同步更新目录列表接口路径
- Updated: API文档更新，反映目录列表接口的路径变更

## v0.9.23 - 2025-07-31 18:00:00
- Changed: 目录管理API接口路径调整，统一接口命名规范
- Changed: 目录创建接口路径调整：POST /api/directories → POST /api/directories/add
- Changed: 目录列表接口方法调整：GET /api/directories → POST /api/directories
- Changed: 目录详情接口路径调整：GET /api/directories/{directory_id} → GET /api/directories/info/{directory_id}
- Changed: 目录编辑接口路径调整：PUT /api/directories/{directory_id} → PUT /api/directories/edit/{directory_id}
- Added: DirectoryListRequest 请求模型，支持 JSON 格式的目录列表查询参数
- Improved: 目录接口路由顺序优化，避免路径匹配冲突
- Updated: 前端API客户端新增 getDirectoryInfo 方法，更新相关方法路径
- Updated: API文档更新，反映目录管理接口的路径变更

## v0.9.22 - 2025-07-31 17:30:00
- Changed: 图片管理API接口路径调整，统一接口命名规范
- Changed: 图片详情接口路径调整：GET /api/images/{image_uuid}/info → GET /api/images/info/{image_uuid}
- Changed: 图片列表接口方法和路径调整：GET /api/images → POST /api/images/list
- Added: ImageListRequest 请求模型，支持 JSON 格式的图片列表查询参数
- Improved: 图片接口路由顺序优化，避免路径匹配冲突
- Updated: 前端API客户端新增 getImageList 方法，更新 getImageInfo 方法路径
- Updated: API文档更新，反映图片管理接口的路径变更

## v0.9.21 - 2025-07-31 17:00:00
- Changed: 管理模块API接口路径重构，统一接口命名规范
- Changed: 列表接口路径调整：GET /api/{module} → GET /api/{module}/list（actors、tags、series、genres）
- Changed: 创建接口路径调整：POST /api/{module} → POST /api/{module}/add（actors、tags、series、genres）
- Changed: 详情接口路径调整：GET /api/{module}/{id} → GET /api/{module}/info/{id}（actors、tags、series、genres）
- Changed: 编辑接口路径调整：PUT /api/{module}/{id} → PUT /api/{module}/edit/{id}（actors、tags、series、genres）
- Improved: API路径结构更加清晰和一致，避免路由匹配冲突
- Updated: 前端API客户端同步更新所有相关方法的请求路径
- Updated: API文档更新，反映所有管理模块接口的路径变更

## v0.9.20 - 2025-07-31 16:30:00
- Changed: 收藏添加接口整合，将原有的两个添加端点（POST /api/favorites/batch 和 POST /api/favorites/{movie_id}）合并为统一的 POST /api/favorites/add 接口
- Added: 统一的添加收藏接口，支持通过 movie_ids 数组进行单个或批量添加操作
- Improved: 智能处理单个和批量添加逻辑，单个影片时返回详细收藏信息，批量时返回统计信息
- Updated: 前端API客户端新增 addFavorites 统一方法，保留原有方法作为兼容性接口
- Updated: API文档更新，反映收藏添加接口的整合变更

## v0.9.19 - 2025-07-31 16:00:00
- Changed: 收藏列表接口路径调整，将 POST /api/favorites 调整为 POST /api/favorites/list
- Improved: API路径结构更加清晰，避免与其他收藏操作接口的路径冲突
- Updated: 前端API客户端更新，使用新的收藏列表接口路径
- Updated: API文档更新，反映收藏列表接口的路径变更

## v0.9.18 - 2025-07-31 15:45:00
- Changed: 收藏删除接口整合，将原有的两个删除端点（DELETE /api/favorites/batch 和 DELETE /api/favorites/{movie_id}）合并为统一的 DELETE /api/favorites 接口
- Added: 新增 DeleteFavoriteRequest 请求模型，支持通过 movie_ids 数组进行单个或批量删除操作
- Improved: 统一的删除接口设计，简化API结构，提升开发和维护效率
- Improved: 删除操作的错误处理和响应格式保持一致性
- Removed: 删除旧的批量删除接口 DELETE /api/favorites/batch
- Removed: 删除旧的单个删除接口 DELETE /api/favorites/{movie_id}
- Updated: API文档更新，反映新的接口变更和统一的删除操作方式

## v0.9.17 - 2025-07-30 22:30:00
- Added: 全新的收藏页面功能，提供专门的收藏影片管理界面
- Added: 收藏页面后端路由 `/favorites`，渲染收藏页面模板
- Added: 扩展收藏API功能，新增 `POST /api/favorites` 端点支持分页、搜索和筛选
- Added: 收藏过滤参数模型 `FavoriteFilterParams`，支持按标题、演员、分类、系列、标签、年份、评分等条件筛选
- Added: 收藏服务增强，支持复杂查询条件和多字段排序功能
- Added: 收藏页面HTML模板 `favorites.html`，基于影片库页面设计，保持界面风格一致性
- Added: 收藏页面JavaScript脚本 `favorites.js`，实现完整的前端交互功能
- Added: 收藏页面搜索功能，支持实时搜索和防抖处理
- Added: 收藏页面高级筛选功能，支持分类、系列、标签、年份范围、评分范围等多维度筛选
- Added: 收藏页面排序功能，支持按收藏时间、标题、年份、评分等字段排序
- Added: 收藏页面分页功能，支持大量收藏数据的分页显示
- Added: 收藏页面空状态显示，当无收藏时显示友好的引导界面
- Added: 收藏页面批量操作支持，集成现有的批量收藏管理功能
- Added: 导航菜单更新，在主导航和移动端导航中添加收藏页面入口
- Added: 收藏页面URL参数同步，支持搜索、排序、分页等状态的URL持久化
- Added: 收藏页面响应式设计，确保在不同屏幕尺寸下的良好体验
- Added: 前端API客户端扩展，新增 `getFavorites` 方法支持收藏列表获取
- Improved: 收藏功能的用户体验，提供专门的收藏管理界面
- Improved: 系统导航结构，增加收藏页面的便捷访问入口

## v0.9.16 - 2025-07-31 18:45:00
- Added: 批量移除收藏功能，支持同时移除多个影片的收藏状态
- Added: 后端API新增 `DELETE /api/favorites/batch` 端点，支持批量移除收藏操作
- Added: 前端批量操作工具栏智能显示逻辑，根据选中影片收藏状态动态显示操作按钮
- Added: 混合状态支持，当选中影片包含已收藏和未收藏时，同时显示批量收藏和批量移除收藏按钮
- Added: 批量移除收藏的前端交互功能，包括操作确认和结果反馈
- Added: 批量移除收藏的完整测试用例，验证API和前端功能的正确性
- Improved: 收藏功能用户体验，提供更灵活的批量操作选项
- Improved: 批量操作工具栏的智能化程度，提升操作效率

## v0.9.15 - 2025-07-31 01:00:00
- Removed: 移除影片详情API接口 `/api/movies/{num}` 及其相关代码
- Removed: 移除后端MovieService中的get_movie_by_num方法
- Changed: 前端movie-detail.js和movie-edit.js中通过num获取影片的逻辑改为先搜索获取ID再调用ID接口
- Changed: 统一使用 `/api/movies/id/{movie_id}` 接口获取影片详情
- Added: 前端新增getMovieIdByNum函数，通过影片列表API搜索num对应的影片ID
- Fixed: 确保所有通过num访问的功能仍能正常工作
- Improved: 简化API结构，减少重复的接口端点

## v0.9.14 - 2025-07-31 00:30:00
- Changed: 影片列表API接口从GET改为POST方法，支持复杂过滤参数
- Changed: 影片列表API参数传递方式从URL查询字符串改为JSON请求体
- Changed: 前端API调用方法更新，使用POST请求获取影片列表
- Changed: 移除前端数组参数转字符串的处理逻辑，直接传递数组格式
- Improved: 支持更复杂的多选过滤条件，提升搜索功能的灵活性
- Improved: API参数验证通过Pydantic模型自动处理，提升代码质量

## v0.9.13 - 2025-07-31 00:00:00
- Fixed: 高级搜索功能中多选下拉组件数据提交失败问题
- Fixed: 后端API增加对多选标签、分类、系列过滤的支持
- Fixed: 前端高级搜索表单数据收集逻辑，正确获取多选下拉框选中值
- Fixed: 影片编辑页面取消按钮功能失效问题，现在可以正确返回详情页
- Fixed: 影片编辑页面返回按钮路径错误，修正为正确的详情页路径
- Changed: 后端MovieFilterParams模式支持年份和评分范围过滤
- Changed: 前端API调用参数处理，支持数组参数转换为逗号分隔字符串
- Added: 后端API支持tag_ids、genre_ids、series_ids多选过滤参数
- Added: 后端API支持year_from、year_to、rating_from、rating_to范围过滤参数

## v0.9.12 - 2025-07-30 23:50:00
- Changed: 标签管理页面图标系统完全重构，使用Bootstrap Icons替代所有SVG
- Changed: 系列管理页面添加按钮图标优化
- Changed: 标签管理统计卡片图标更新，提升数据可视化效果
- Changed: 标签管理空状态图标优化，提供更好的用户引导
- Removed: 移除标签管理页面中所有22个内联SVG代码
- Fixed: 标签管理页面图标可访问性改进，添加完整的aria-label属性
- Fixed: 管理页面图标风格统一，提升整体用户体验

## v0.9.11 - 2025-07-30 23:45:00
- Changed: 系列管理页面图标系统完全重构，使用Bootstrap Icons替代所有SVG
- Changed: 系列管理搜索框和操作按钮图标优化
- Changed: 系列管理统计卡片图标更新，提升数据可视化效果
- Changed: 系列管理空状态图标优化，提供更好的用户引导
- Removed: 移除系列管理页面中所有18个内联SVG代码
- Fixed: 系列管理页面图标可访问性改进，添加完整的aria-label属性
- Fixed: 管理页面图标风格完全统一，提升整体用户体验

## v0.9.10 - 2025-07-30 23:30:00
- Changed: 影片详情页面图标系统完全重构，使用Bootstrap Icons替代所有SVG
- Changed: 影片详情页面操作按钮图标优化，包括编辑、同步NFO、刷新等功能
- Changed: NFO同步功能状态图标更新，提升用户反馈体验
- Changed: 影片详情页面错误和成功状态图标优化
- Changed: 模态框关闭按钮图标统一，提升界面一致性
- Removed: 移除影片详情相关页面中所有20个内联SVG代码
- Fixed: 影片详情页面图标可访问性改进，添加完整的aria-label属性
- Fixed: NFO同步流程图标语义化，提升用户操作理解度

## v0.9.12 - 2025-07-30 23:45:00
- Changed: 影片编辑页面图标系统完全重构，使用Bootstrap Icons替代所有SVG
- Changed: 影片编辑页面操作按钮图标优化，包括返回、保存、取消等功能
- Changed: 影片编辑页面错误状态图标更新，提升用户反馈体验
- Changed: 影片编辑JavaScript中动态按钮图标优化
- Removed: 移除影片编辑相关页面中所有12个内联SVG代码
- Fixed: 影片编辑页面图标可访问性改进，添加完整的aria-label属性
- Fixed: 影片编辑界面图标风格统一，提升用户操作体验

## v0.9.9 - 2025-07-30 22:45:00
- Changed: 数据管理页面图标系统重构，包括分类、演员、系列、标签管理
- Changed: 分类管理页面完全使用Bootstrap Icons替代SVG
- Changed: 演员管理页面图标优化，统一视觉风格
- Changed: 系列管理页面图标更新，提升用户体验
- Changed: 标签管理页面图标系统优化
- Changed: 数据管理通用组件图标更新，包括编辑和删除按钮
- Removed: 移除管理页面中所有88个内联SVG代码
- Fixed: 管理页面统计卡片图标语义化，提升可读性
- Fixed: 空状态和操作按钮图标可访问性改进

## v0.9.8 - 2025-07-30 22:30:00
- Changed: 影片详情模态框图标系统优化，使用Bootstrap Icons替代SVG
- Changed: 多选下拉组件图标更新，包括下拉箭头和标签移除按钮
- Changed: 单选下拉组件图标优化，包括清除按钮、下拉箭头和选中状态图标
- Changed: 组件交互图标统一，提升用户界面一致性和可访问性
- Removed: 移除组件库中所有17个内联SVG代码
- Fixed: 下拉组件DOM选择器更新，确保图标功能正常工作
- Fixed: 组件图标可访问性改进，添加完整的aria-label属性

## v0.9.7 - 2025-07-30 22:15:00
- Changed: 影片库页面图标系统完全重构，使用Bootstrap Icons替代所有SVG
- Changed: 影片卡片设计优化，包括默认海报、播放按钮、分类和演员徽章图标
- Changed: 搜索和筛选功能图标更新，提升用户交互体验
- Changed: 分页导航图标优化，使用直观的左右箭头图标
- Changed: 排序下拉菜单图标统一，增强功能识别度
- Changed: 空状态和错误状态图标优化，提供更好的视觉反馈
- Removed: 移除影片库相关页面中所有64个内联SVG代码
- Removed: 清理JavaScript中动态生成的影片卡片SVG图标
- Fixed: 影片库界面图标可访问性改进，添加完整的aria-label属性

## v0.9.6 - 2025-07-30 21:45:00
- Changed: 目录管理页面图标系统完全重构，使用Bootstrap Icons替代所有SVG
- Changed: 目录卡片操作按钮图标优化，包括编辑、扫描、启用/停用、删除等功能
- Changed: 目录浏览器图标更新，使用语义化的文件夹和导航图标
- Changed: 模态框关闭按钮图标统一，提升用户界面一致性
- Changed: 搜索框、刷新按钮、批量操作等功能图标优化
- Removed: 移除目录管理相关页面中所有42个内联SVG代码
- Removed: 清理JavaScript动态生成的SVG图标代码
- Fixed: 补充所有缺失的按钮图标，确保界面元素完整性
- Fixed: 图标可访问性改进，为所有新增图标添加aria-label属性

## v0.9.5 - 2025-07-30 21:15:00
- Added: 引入Bootstrap Icons图标库，提供统一的图标系统
- Changed: 全站图标系统完全重构，使用语义化的Bootstrap Icons替代所有内联SVG
- Changed: 导航菜单图标优化，包括移动端和桌面端所有导航元素
- Changed: 管理菜单图标统一，使用直观的图标表示各功能模块
- Changed: 主题切换器图标优化，使用调色板图标增强识别度
- Changed: 高级搜索表单图标更新，提升表单元素的视觉层次
- Changed: 品牌Logo图标替换，使用电影胶卷图标突出媒体管理主题
- Removed: 移除base.html中所有54个内联SVG代码，大幅减少HTML文件大小
- Removed: 清理仪表板和JavaScript中动态生成的SVG图标代码
- Fixed: 图标可访问性全面改进，为所有图标添加aria-label属性支持屏幕阅读器
- Fixed: 图标尺寸和颜色统一，确保在不同主题下的一致性表现

## v0.9.4 - 2025-07-30 20:45:00
- Added: 目录封面自动生成功能，支持随机选择4张海报进行拼接
- Added: 增量扫描和全量扫描模式选择，用户可根据需要选择扫描方式
- Added: 目录卡片响应式布局，支持不同屏幕尺寸的自适应显示
- Added: 批量目录扫描功能，支持同时扫描多个选中目录
- Added: 扫描进度和结果的详细反馈，显示处理文件数和新增影片数
- Changed: 目录扫描API参数传递方式，从查询参数改为JSON请求体
- Changed: 目录卡片设计优化，采用350:197宽高比，提升视觉效果
- Changed: 扫描结果显示逻辑，根据实际扫描类型显示对应的完成消息
- Changed: 前端错误处理机制，统一使用Toast通知显示操作结果
- Fixed: 修复目录扫描API参数传递问题，确保增量/全量模式正确识别
- Fixed: 修复封面生成数据库写入问题，确保封面数据正确保存
- Fixed: 修复目录卡片事件绑定问题，确保按钮功能正常工作
- Fixed: 修复批量扫描选择状态管理，避免状态不一致问题
- Removed: 彻底清理所有JavaScript文件中的调试代码（console.log、console.debug、console.info、console.warn）
- Removed: 清理HTML模板中的内联调试JavaScript代码
- Removed: 移除开发阶段的临时调试输出，保持生产环境代码简洁

## v0.9.3 - 2025-07-30 18:15:00
- Added: 目录管理页面的搜索功能，支持按路径和描述搜索
- Added: 目录状态切换功能，可快速启用/禁用目录
- Added: 目录删除确认对话框，防止误删重要目录
- Changed: 优化目录列表渲染性能，减少DOM操作次数
- Changed: 改进目录卡片的视觉设计，增强用户体验
- Fixed: 修复目录编辑表单验证问题
- Fixed: 修复目录列表加载错误处理

## v0.9.2 - 2025-07-30 16:30:00
- Added: 目录封面自动生成功能，支持4张海报拼接
- Added: 增量扫描和全量扫描模式选择
- Added: 目录卡片响应式布局优化
- Changed: 目录扫描默认改为增量扫描模式
- Changed: 目录卡片设计优化，采用350:197宽高比
- Fixed: 修复目录扫描API参数传递问题
- Fixed: 修复封面生成数据库写入问题
- Removed: 清理项目中的调试代码和console.log语句

## v0.9.1 - 2025-07-29 18:00:00
- Changed: 前端UI框架从Bootstrap/Tabler完全迁移到DaisyUI v5
- Changed: 使用DaisyUI CDN方式引入，移除所有本地CSS文件
- Changed: 主题切换功能迁移到DaisyUI原生theme controller组件
- Changed: 下拉菜单功能迁移到DaisyUI原生dropdown组件
- Changed: Toast通知系统迁移到DaisyUI alert组件
- Changed: 模态框组件迁移到DaisyUI原生modal组件
- Changed: 导航栏、卡片、按钮、表单等所有组件使用DaisyUI样式
- Changed: 响应式布局从Bootstrap Grid迁移到Tailwind CSS Grid/Flexbox
- Removed: 删除static/js/theme.js和static/js/dropdown.js自定义JavaScript文件
- Removed: 删除static/css/style.css自定义样式文件
- Removed: 移除所有Bootstrap和Tabler相关CDN引用
- Fixed: 主题切换状态的本地存储功能继续正常工作
- Fixed: 深色/浅色主题切换功能在DaisyUI下正常工作
- Improved: 使用DaisyUI原生组件提供更好的一致性和性能
- Improved: 简化了前端代码结构，减少了自定义JavaScript依赖

## v0.8.5 - 2025-07-29 17:30:00
- Fixed: 修复 Toast 通知在深色主题下没有对应彩色配色的问题
- Fixed: 修复成功、失败、警告、信息类型的 Toast 在深色主题下全是黑色的问题
- Fixed: 修复 Toast 头部、主体和关闭按钮在深色主题下的样式问题
- Fixed: 修复 Toast 进度条在深色主题下的显示效果
- Fixed: 修复加载中 Toast 的旋转图标在深色主题下的颜色
- Fixed: 修复确认对话框样式的 Toast 按钮在深色主题下的显示
- Improved: 为不同类型的 Toast 添加彩色边框以增强视觉区分
- Improved: 优化 Toast 在深色主题下的颜色对比度和可读性
- Improved: 添加 Toast 阴影效果以增强立体感
- Improved: 为 Toast 添加主题切换时的平滑过渡动画
- Added: 新增 Toast 主题切换的强制样式更新机制
- Added: 完整的浅色和深色主题 Toast 样式支持

## v0.8.4 - 2025-07-29 17:00:00
- Fixed: 修复 alert-success 警告框在深色主题下没有适配的问题
- Fixed: 修复所有类型的警告框(alert-info, alert-warning, alert-danger, alert-primary, alert-secondary)在深色主题下的样式
- Fixed: 修复警告框中链接(alert-link)在深色主题下的颜色显示
- Fixed: 修复警告框中关闭按钮在深色主题下的显示问题
- Fixed: 修复可关闭警告框(alert-dismissible)在深色主题下的样式
- Improved: 为警告框添加主题切换时的平滑过渡动画
- Improved: 优化主题切换时警告框样式的实时更新机制
- Added: 新增完整的浅色主题警告框样式以确保一致性
- Added: 新增警告框图标在不同主题下的透明度优化
- Added: 强制样式更新机制确保主题切换时警告框能立即更新

## v0.8.3 - 2025-07-29 16:30:00
- Fixed: 修复表格头部(thead)在深色主题下仍显示为浅色的问题
- Fixed: 修复卡片底部(card-footer)在深色主题下仍显示为浅色的问题
- Fixed: 修复卡片头部(card-header)在深色主题下的文字颜色问题
- Fixed: 修复卡片表格(card-table)在深色主题下的样式问题
- Fixed: 修复表格悬停效果在深色主题下的显示问题
- Fixed: 修复边框表格(table-bordered)在深色主题下的边框颜色
- Fixed: 修复分页组件在浅色主题下的样式一致性
- Fixed: 修复表格响应式容器的滚动条在深色主题下的样式
- Improved: 为表格和卡片元素添加主题切换的平滑过渡动画
- Improved: 优化主题切换时表格和卡片样式的实时更新机制
- Added: 新增卡片标题和操作区域的深色主题样式支持
- Added: 新增表格条纹和悬停效果的完整主题支持

## v0.8.2 - 2025-07-29 16:00:00
- Fixed: 修复模态框在深色主题模式下的样式问题
- Fixed: 模态框头部、主体和底部在深色主题下的背景色和文字颜色
- Fixed: 模态框中表单元素（输入框、选择框、复选框）的深色主题样式
- Fixed: 模态框中警告框和信息框的深色主题显示效果
- Fixed: 模态框关闭按钮在深色主题下的显示问题
- Improved: 添加模态框主题切换时的平滑过渡效果
- Improved: 优化模态框背景遮罩在不同主题下的透明度
- Improved: 确保主题切换时已打开的模态框能立即更新样式
- Added: 新增CSS变量以确保模态框样式与整体主题保持一致
- Added: 强制样式更新机制，确保主题切换的实时性

## v0.8.1 - 2025-07-29 15:30:00
- Changed: 优化目录删除功能，默认删除目录时同时删除关联的影片数据
- Removed: 移除强制删除选项，简化删除流程
- Improved: 删除确认对话框显示影片数量警告和重要说明
- Improved: 明确说明删除操作不会删除磁盘上的实际文件
- Added: 目录启用/停用状态控制功能
- Added: 停用目录中的影片自动从影片库页面隐藏
- Added: 访问停用目录中影片时显示适当的403错误提示
- Added: 目录卡片中的快速启用/停用切换按钮
- Security: 确保停用目录中的影片无法通过API访问
- Improved: 优化删除确认对话框的视觉设计和用户体验

## v0.8.0 - 2025-07-28 17:00:00
- Added: 在影片详情页面添加"同步到NFO"功能按钮
- Added: 完整的NFO同步服务，支持将数据库信息同步回NFO文件
- Added: 增量更新机制，仅更新已修改的字段，保留NFO文件原有信息
- Added: NFO文件权限检查和路径安全验证功能
- Added: 同步前确认对话框，显示NFO文件路径和注意事项
- Added: 同步结果对话框，详细显示更新的字段和文件路径
- Added: 支持同步标题、年份、评分、剧情、分类、标签、演员、系列等核心字段
- Added: 新增API端点 `POST /api/movies/id/{movie_id}/sync-nfo` 执行同步
- Added: 新增API端点 `GET /api/movies/id/{movie_id}/nfo-status` 检查NFO状态
- Security: 实现路径遍历攻击防护和文件权限验证
- Improved: 优化XML格式化输出，确保NFO文件结构清晰
- Improved: 完善的错误处理机制，包括文件不存在、权限不足等异常情况

## v0.7.6 - 2025-07-28 16:30:00
- Fixed: 修复影片编辑页面中分类、标签和演员列表的徽章间距显示问题
- Improved: 为已选择的分类、标签和演员容器添加 `badge-container` CSS类
- Improved: 移除徽章上的 `me-1 mb-1` 类，使用CSS gap属性统一控制间距
- Improved: 确保徽章在水平和垂直方向都有适当的间距（0.25rem）
- Improved: 统一编辑页面和详情页面的徽章间距样式
- Fixed: 修复新增和删除徽章时的间距自动调整功能
- Improved: 优化徽章换行显示时的行间距效果

## v0.7.5 - 2025-07-28 16:00:00
- Fixed: 修复影片编辑页面保存功能的HTTP 405错误问题
- Added: 新增 `PUT /api/movies/id/{movie_id}` API路由，匹配前端请求URL格式
- Added: 完整的演员管理API，包括演员的CRUD操作和搜索功能
- Added: 演员编辑界面优化，采用与分类和标签相同的下拉复选框设计模式
- Added: 演员选择支持搜索过滤和新演员创建功能
- Added: 演员数据的实时计数显示和删除功能
- Improved: 统一了所有选择器的交互体验和视觉设计
- Fixed: 确保影片编辑保存后正确跳转到详情页面并显示更新后的数据

## v0.7.4 - 2025-07-28 14:00:00
- Added: 完整的影片编辑功能，支持编辑所有影片字段（基本信息、分类、标签、演员、剧情等）
- Added: 影片详情页面新增"编辑影片"按钮，支持通过ID和编号两种方式访问编辑页面
- Added: 新增影片编辑页面路由：`/movies/id/{movie_id}/edit` 和 `/movies/{num}/edit`
- Improved: 优化分类和标签的显示逻辑，默认只显示已选中的项目，提升界面简洁性
- Added: 分类和标签支持下拉复选框选择，点击搜索框显示所有可选项目
- Added: 实时搜索过滤功能，支持输入关键词快速查找分类和标签
- Added: 系列选择支持搜索功能的组合框，可搜索现有系列或创建新系列
- Added: 支持创建新的系列、分类、标签，创建成功后自动选中
- Added: 已选择项目支持一键删除，每个徽章都有删除按钮（×）
- Added: 实时计数显示，显示已选择的分类和标签数量
- Added: 防抖处理优化，避免频繁的DOM操作和API调用
- Added: CSS动画效果，包括下拉框的平滑显示和项目的淡入淡出效果
- Added: 完整的表单验证和错误处理，确保数据完整性
- Added: 响应式设计优化，确保在移动端也有良好的交互体验
- Fixed: 优化下拉框的层级和定位，避免界面元素重叠问题

## v0.7.2 - 2025-07-28 12:00:00
- Added: 新增 `.badge-container` CSS类，使用flexbox和gap属性确保徽章换行时的一致间距
- Fixed: 修复影片详情页面中演员、分类、标签徽章换行时的布局问题
- Changed: 移除徽章的margin属性，改用CSS gap属性实现更好的间距控制
- Added: 为徽章添加文本溢出处理，防止超长文本破坏布局
- Improved: 优化移动端统计卡片的内边距和标签字体大小
- Added: 为影片详情页面添加更好的文本换行支持，确保长文本正确显示
- Fixed: 确保所有徽章在不同屏幕尺寸下都有一致的视觉效果
- Improved: 增强响应式设计，在桌面端、平板端、移动端都有完美的布局表现

## v0.7.1 - 2025-07-28 11:30:00
- Removed: 移除影片详情页面中自定义的 `.actor-list`、`.genre-list`、`.tag-list` 和 `.badge-item` CSS样式类
- Changed: 统一使用 Tabler.io 框架的原生 badge 组件显示演员、分类、标签信息
- Changed: 调整API路由结构，将 `/api/movies/{movie_id}` 改为 `/api/movies/id/{movie_id}`，将 `/api/movies/num/{num}` 改为 `/api/movies/{num}`
- Changed: 调整前端路由结构，将 `/movies/{movie_id}` 改为 `/movies/id/{movie_id}`，将 `/movies/num/{num}` 改为 `/movies/{num}`
- Fixed: 确保演员列表在桌面端、平板端、移动端都有完美的响应式表现
- Changed: 减少自定义CSS代码，提升代码维护性和设计一致性
- Added: 支持通过num字段访问影片详情的完整后端和前端实现

## v0.7.0 - 2025-07-28 11:00:00
- Added: 创建独立的影片详情页面，替代原有的模态框展示方式
- Added: 新增路由 `/movies/{movie_id}` 用于显示单个影片的详细信息
- Added: 创建 `movie_detail.html` 模板，提供完整的影片信息展示
- Changed: 修改影片卡片点击事件，从打开模态框改为跳转到详情页面
- Added: 影片详情页面包含海报、标题、评分、分类、系列、标签、演员、剧情简介等完整信息
- Added: 详情页面支持返回影片库的导航功能
- Added: 支持直接通过URL访问特定影片的详情页面，SEO友好
- Added: 详情页面支持浏览器前进/后退功能
- Changed: 优化用户体验，提供更清晰的页面结构和导航
- Removed: 移除影片卡片中的系列名称显示，简化卡片布局

## v0.6.2 - 2025-07-28 10:30:00
- Fixed: 完善影片海报图片显示功能，实现多级备选图片逻辑
- Added: 图片加载失败的fallback处理机制，确保用户体验
- Changed: 优化影片图片URL生成逻辑，优先级：poster_uuid > fanart_uuid > thumb_uuid
- Fixed: 验证并确认影片库页面所有功能正常工作，包括搜索、筛选、分页等
- Fixed: 确认响应式设计在桌面、平板、移动端的完美适配
- Fixed: 验证所有交互功能正常，包括搜索、清除筛选、导航等
- Changed: 改进图片懒加载和错误处理，提升页面性能和稳定性

## v0.6.1 - 2025-07-28 09:45:00
- Removed: 移除 router.js 文件及相关调用代码，简化前端架构
- Changed: 将导航状态管理逻辑直接集成到 base.html 中，无需独立路由模块
- Fixed: 保留下拉菜单活动状态处理功能，确保导航体验不受影响
- Changed: 减少 JavaScript 模块依赖，提升页面加载性能

## v0.6.0 - 2025-07-28 23:45:00
- Added: 完整的模块化前端架构，基于 Tabler.io 设计系统
- Added: 前端集成到 FastAPI 主程序，支持静态文件服务和模板引擎
- Added: 基础模板 base.html，包含导航、主题切换、Toast 容器等公共组件
- Added: 仪表板页面 dashboard.html，显示系统统计和快速操作
- Added: 影片库页面 movies.html，支持搜索、过滤、分页和网格展示
- Added: 标签管理页面 tags_manager.html，支持 CRUD 操作和批量管理
- Added: 分类管理页面 genres_manager.html，支持分类的完整管理
- Added: 系列管理页面 series_manager.html，支持系列的完整管理
- Added: 设置页面 settings.html，包含常规、显示、API 和关于等设置选项
- Added: 核心 JavaScript 模块：utils.js（工具函数）、api.js（API 客户端）
- Added: 主题管理模块 theme.js，支持明暗主题切换和系统主题跟随
- Added: Toast 通知模块 toast.js，支持成功、错误、警告、信息等类型通知
- Added: 路由管理模块 router.js，处理前端路由和页面导航（已在 v0.6.1 中移除）
- Added: 仪表板脚本 dashboard.js，实现数据加载、统计显示和系统状态检查
- Added: 影片库脚本 movies.js，实现影片列表、搜索、过滤、分页功能
- Added: 影片模态框脚本 movie-modals.js，支持详情查看、编辑和删除功能
- Added: 响应式设计，支持桌面、平板、手机等多端适配
- Added: 图片懒加载、防抖搜索、骨架屏等性能优化功能
- Added: 前端路由系统，支持 /、/movies、/tags、/genres、/series、/settings 页面
- Changed: API 端点添加 /api 前缀，前端页面使用根路径
- Changed: 主程序集成 Jinja2 模板引擎和静态文件服务
- Security: 版权信息集成，© 2025 KleinerSource. All rights reserved.

## v0.5.1 - 2025-07-28 19:45:00
- Fixed: 修复影片图片虚拟化关联问题，影片 API 现在正确返回图片 UUID
- Fixed: 修复 MovieService.get_movie_with_image_uuids() 方法的查询逻辑错误
- Fixed: 修复路径匹配问题 - 影片表中存储的是 UUID 而非文件路径
- Added: MovieListItem 响应模型新增 fanart_uuid 和 thumb_uuid 字段
- Added: 批量图片 UUID 查询优化，提高影片列表 API 性能
- Added: 图片 UUID 验证逻辑，确保返回的 UUID 在 v_images 表中存在
- Changed: 优化影片列表 API 使用批量查询减少数据库访问次数
- Tested: 6 项图片虚拟化功能测试，100% 通过率
- Tested: 验证影片列表和详情 API 正确返回有效的图片 UUID
- Tested: 验证图片访问端点 /api/images/{uuid} 正常工作

## v0.5.0 - 2025-07-28 16:45:00
- Changed: 重构媒体扫描 API，将独立的 /scan 模块整合到 /directories 模块
- Removed: 独立的 POST /scan 端点
- Added: POST /directories/{directory_id}/scan - 扫描指定目录
- Added: POST /directories/scan-all - 扫描所有已启用的目录
- Added: incremental 参数支持增量扫描和全量扫描
- Added: 增量扫描逻辑 - 基于文件修改时间过滤文件
- Added: 更新现有影片功能 - 区分新增和更新操作
- Added: 数据库服务新方法 - get_movie_by_file_path, update_movie_data
- Changed: 扫描结果始终保存到数据库，移除 save_to_db 参数
- Changed: 使用数据库中的目录记录，移除 directory 路径参数
- Added: 详细的扫描统计信息 - 处理文件数、新增影片数、更新影片数
- Added: 完整的错误处理 - 目录不存在、目录未启用、扫描失败等
- Removed: app/api/scan.py 文件
- Changed: main.py 中移除 scan 模块的路由注册
- Tested: 7 项扫描功能测试，71.4% 通过率（主要功能正常）

## v0.4.0 - 2025-07-28 16:00:00
- Added: 完整的影片管理 API 模块 (app/api/movies.py)
- Added: 影片列表 API (GET /movies) - 支持分页、搜索、多维度过滤
- Added: 影片详情 API (GET /movies/{movie_id}) - 返回完整影片信息和关联数据
- Added: 影片更新 API (PUT /movies/{movie_id}) - 支持更新基本信息和关联关系
- Added: 影片删除 API (DELETE /movies/{movie_id}) - 仅删除数据库记录，保留物理文件
- Added: 影片统计 API (GET /movies/stats/overview) - 提供影片库统计信息
- Added: 影片服务类 (app/services/movie_service.py) - 完整的业务逻辑层
- Added: 影片相关 Pydantic 模型 - MovieUpdate, MovieResponse, MovieListItem 等
- Added: 高级搜索功能 - 支持按标题、演员、剧情搜索
- Added: 多维度过滤 - 支持按分类、系列、年份、评分过滤
- Added: 虚拟图片 UUID 集成 - 海报、剧照、缩略图的虚拟化访问
- Added: 关联数据管理 - 演员、分类、系列、标签的关联关系更新
- Added: 完整的错误处理和参数验证
- Tested: 7 项基础功能测试，100% 通过率
- Changed: API 文档新增"影片库"分组，改善文档组织

## v0.3.0 - 2025-07-28 15:15:00
- Changed: 重构 API 路由结构，将所有 API 端点从 main.py 迁移到模块化路由
- Added: 创建 app/api/tags.py - 标签管理 API 路由模块
- Added: 创建 app/api/genres.py - 分类管理 API 路由模块
- Added: 创建 app/api/series.py - 系列管理 API 路由模块
- Added: 创建 app/api/directories.py - 目录管理 API 路由模块
- Added: 创建 app/api/images.py - 图片管理 API 路由模块
- Added: 创建 app/api/scan.py - 媒体扫描 API 路由模块
- Changed: main.py 从 1042 行代码精简到 71 行，只保留基础端点和路由注册
- Changed: 使用 FastAPI 的 include_router() 方法注册各模块路由
- Added: 为每个 API 模块设置适当的前缀和标签，改善 API 文档组织
- Fixed: 解决路径冲突问题，确保所有端点正确映射
- Tested: 9 项重构验证测试，100% 通过率，确保功能完整性
- Changed: 提升代码可维护性、可读性和团队协作效率

## v0.2.0 - 2025-07-28 14:30:00
- Added: 完整的标签管理 API，支持 CRUD 操作、分页、搜索和关联检查
- Added: 完整的分类管理 API，支持 CRUD 操作、分页、搜索和关联检查
- Added: 完整的系列管理 API，支持 CRUD 操作、分页、搜索和关联检查
- Added: Pydantic 模型用于请求验证和响应序列化
- Added: 管理服务类（TagService、GenreService、SeriesService）
- Added: 强制删除功能，支持忽略关联关系的删除操作
- Added: 统一的响应格式和错误处理机制
- Added: 完整的边界情况测试（重复名称、空值、不存在的ID等）
- Fixed: Pydantic V2 兼容性问题，使用 model_validate 替代 from_orm
- Tested: 20 项管理 API 测试，100% 通过率

## v0.1.1 - 2025-07-28 13:45:00
- Added: 图片虚拟化功能，为每个图片文件生成唯一的 UUID 映射
- Added: v_images 表数据自动创建，支持 poster、fanart、thumb 等图片类型
- Added: 图片虚拟化访问 API，支持通过 UUID 访问图片文件
- Added: 图片信息查询 API，获取图片详细信息和文件状态
- Added: 图片列表 API，支持按类型过滤和分页查询
- Fixed: DatabaseService 中缺少图片虚拟化处理的问题
- Fixed: 电影记录中图片路径现在存储 UUID 而非真实路径
- Tested: 图片虚拟化功能完整测试，包括数据库创建、API 访问和类型过滤

## v0.1.0 - 2025-07-28 13:00:00
- Added: 项目初始化，使用 uv 作为 Python 包管理器
- Added: 基础项目结构，包含 app/models、app/services、app/api、app/core 目录
- Added: SQLAlchemy 数据库模型设计，包含 movies、tags、genres、actors、series、directories、v_images、configs 表
- Added: 多对多关联表设计（movie_tags、movie_genres、movie_actors）
- Added: Alembic 数据库迁移配置和初始迁移脚本
- Added: NFO 文件解析器，支持解析 Kodi/Jellyfin 格式的 XML 元数据文件
- Added: 媒体文件扫描器，支持扫描视频文件及相关的 NFO 和图片文件
- Added: 跨平台兼容的数据库配置（使用 SQLite）
- Added: 目录管理服务，支持媒体目录的完整 CRUD 操作
- Added: 数据库集成服务，支持将扫描结果保存到数据库并建立关联关系
- Added: 完整的 FastAPI RESTful API，包含目录管理、媒体扫描、数据库统计等接口
- Added: 综合测试脚本，覆盖目录管理、媒体扫描、数据库集成和 API 接口测试
- Added: 详细的测试报告和 README.md 文档
- Fixed: NFO 解析器中演员和系列信息的标签名称兼容性（支持 name 和 n 标签）
- Fixed: 系列信息解析问题，正确识别电影系列/合集信息
- Tested: 完成 32 项综合测试，通过率 96.9%，验证了所有核心功能
