{% extends "base.html" %}

{% block title %}仪表板 - 媒体管理器{% endblock %}

{% block page_header %}
{% endblock %}

{% block content %}
<!-- 加载状态 -->
<div id="loading-overlay" class="fixed inset-0 bg-base-100/80 backdrop-blur-sm z-50 flex items-center justify-center">
    <div class="text-center">
        <span class="loading loading-spinner loading-lg text-primary"></span>
        <p class="mt-4 text-base-content/70">正在加载仪表板数据...</p>
    </div>
</div>

<!-- 最新影片轮播区域 -->
<section class="mb-8">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-base-content flex items-center gap-2">
            <i class="bi bi-film text-primary" aria-label="影片图标"></i>
            最新影片
        </h2>
        <a href="/movies" class="btn btn-outline btn-sm">
            查看全部
            <i class="bi bi-arrow-right ml-1" aria-label="箭头图标"></i>
        </a>
    </div>
    
    <!-- 轮播图容器 -->
    <div class="carousel w-full h-64 md:h-80 lg:h-96 rounded-lg overflow-hidden shadow-lg" id="latest-movies-carousel">
        <!-- 轮播项将由 JavaScript 动态生成 -->
    </div>
    
    <!-- 轮播指示器 -->
    <div class="flex justify-center w-full py-2 gap-2" id="carousel-indicators">
        <!-- 指示器将由 JavaScript 动态生成 -->
    </div>
</section>

<!-- 媒体目录展示区域 -->
<section class="mb-8">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-base-content flex items-center gap-2">
            <i class="bi bi-folder text-primary" aria-label="目录图标"></i>
            媒体目录
        </h2>
        <a href="/directories" class="btn btn-outline btn-sm">
            管理目录
            <i class="bi bi-gear ml-1" aria-label="设置图标"></i>
        </a>
    </div>
    
    <!-- 目录横向滚动容器 -->
    <div class="overflow-x-auto pb-4">
        <div class="flex gap-4 min-w-max" id="directories-container">
            <!-- 目录项将由 JavaScript 动态生成 -->
        </div>
    </div>
</section>

<!-- 最近添加影片区域 -->
<section class="mb-8">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-base-content flex items-center gap-2">
            <i class="bi bi-clock text-primary" aria-label="时钟图标"></i>
            最近添加
        </h2>
        <a href="/movies?sort=created_at&order=desc" class="btn btn-outline btn-sm">
            查看更多
            <i class="bi bi-arrow-right ml-1" aria-label="箭头图标"></i>
        </a>
    </div>
    
    <!-- 影片网格容器 -->
    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4" id="recent-movies-grid">
        <!-- 影片项将由 JavaScript 动态生成 -->
    </div>
</section>

<!-- 统计信息区域 -->
<section class="mb-8">
    <h2 class="text-2xl font-bold text-base-content flex items-center gap-2 mb-6">
        <i class="bi bi-bar-chart text-primary" aria-label="统计图标"></i>
        统计信息
    </h2>
    
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <!-- 总影片数 -->
        <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-primary">
                <i class="bi bi-film text-2xl" aria-label="影片图标"></i>
            </div>
            <div class="stat-title">总影片数</div>
            <div class="stat-value text-primary" id="total-movies">-</div>
        </div>
        
        <!-- 总目录数 -->
        <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-secondary">
                <i class="bi bi-folder text-2xl" aria-label="目录图标"></i>
            </div>
            <div class="stat-title">媒体目录</div>
            <div class="stat-value text-secondary" id="total-directories">-</div>
        </div>
        
        <!-- 收藏数 -->
        <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-accent">
                <i class="bi bi-heart-fill text-2xl" aria-label="收藏图标"></i>
            </div>
            <div class="stat-title">收藏影片</div>
            <div class="stat-value text-accent" id="total-favorites">-</div>
        </div>
        
        <!-- 今日添加 -->
        <div class="stat bg-base-200 rounded-lg">
            <div class="stat-figure text-info">
                <i class="bi bi-plus-circle text-2xl" aria-label="添加图标"></i>
            </div>
            <div class="stat-title">今日添加</div>
            <div class="stat-value text-info" id="today-added">-</div>
        </div>
    </div>
</section>

<!-- 错误提示 -->
<div id="error-alert" class="alert alert-error hidden mb-4">
    <i class="bi bi-exclamation-triangle" aria-label="错误图标"></i>
    <span id="error-message">加载数据时发生错误</span>
    <button type="button" class="btn btn-sm btn-ghost" onclick="location.reload()">
        重试
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/dashboard.js?v=0.9.25"></script>
{% endblock %}
