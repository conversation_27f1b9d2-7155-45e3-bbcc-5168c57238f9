"""
目录管理服务
用于管理媒体目录的 CRUD 操作
"""
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import or_
from app.models.models import Directory, DirectoryPath
import logging
import os

logger = logging.getLogger(__name__)


class DirectoryService:
    """目录管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    

    
    def get_directory_by_id(self, directory_id: int) -> Optional[Directory]:
        """
        根据 ID 获取目录记录
        
        Args:
            directory_id: 目录 ID
            
        Returns:
            目录对象，如果不存在则返回 None
        """
        try:
            return self.db.query(Directory).filter(Directory.id == directory_id).first()
        except Exception as e:
            logger.error(f"获取目录记录时发生错误: {e}")
            return None
    
    def get_directory_by_path(self, path: str) -> Optional[Directory]:
        """
        根据路径获取目录记录

        Args:
            path: 目录路径

        Returns:
            目录对象，如果不存在则返回 None
        """
        try:
            # 通过 DirectoryPath 表查找目录
            directory_path = self.db.query(DirectoryPath).filter(DirectoryPath.path == path).first()
            if directory_path:
                return directory_path.directory
            return None
        except Exception as e:
            logger.error(f"获取目录记录时发生错误: {e}")
            return None
    
    def get_all_directories(self, enabled_only: bool = False) -> List[Directory]:
        """
        获取所有目录记录
        
        Args:
            enabled_only: 是否只返回启用的目录
            
        Returns:
            目录列表
        """
        try:
            query = self.db.query(Directory)
            if enabled_only:
                query = query.filter(Directory.enabled == True)
            return query.all()
        except Exception as e:
            logger.error(f"获取目录列表时发生错误: {e}")
            return []
    
    def update_directory_basic_info(self, directory_id: int, **kwargs) -> Optional[Directory]:
        """
        更新目录基本信息（不包括路径）

        Args:
            directory_id: 目录 ID
            **kwargs: 要更新的字段（name, enabled等）

        Returns:
            更新后的目录对象，如果失败则返回 None

        Raises:
            ValueError: 当名称重复时
        """
        try:
            directory = self.get_directory_by_id(directory_id)
            if not directory:
                logger.error(f"目录记录不存在: {directory_id}")
                return None

            # 检查名称唯一性
            if 'name' in kwargs:
                existing_name = self.db.query(Directory).filter(
                    Directory.name == kwargs['name'],
                    Directory.id != directory_id
                ).first()
                if existing_name:
                    raise ValueError(f"目录名称 '{kwargs['name']}' 已存在")

            # 更新字段（排除路径相关字段）
            allowed_fields = ['name', 'enabled', 'last_scan_time', 'cover_image_base64', 'cover_generated_at']
            for key, value in kwargs.items():
                if key in allowed_fields and hasattr(directory, key):
                    setattr(directory, key, value)

            self.db.commit()
            self.db.refresh(directory)

            logger.info(f"成功更新目录基本信息: {directory_id}")
            return directory

        except ValueError:
            # 重新抛出验证错误
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新目录基本信息时发生错误: {e}")
            return None
    
    def delete_directory(self, directory_id: int) -> bool:
        """
        删除目录记录及其关联的所有影片数据

        注意：此操作只删除数据库中的记录，不会删除磁盘上的实际文件

        Args:
            directory_id: 目录 ID

        Returns:
            是否删除成功
        """
        try:
            directory = self.get_directory_by_id(directory_id)
            if not directory:
                logger.error(f"目录记录不存在: {directory_id}")
                return False

            # 删除目录下的所有影片及其关联数据
            deleted_count = 0
            for directory_path in directory.paths:
                deleted_count += self._delete_movies_in_directory(directory_path.path)

            # 删除目录记录
            self.db.delete(directory)
            self.db.commit()

            logger.info(f"成功删除目录记录: {directory_id}，删除了 {deleted_count} 部影片的数据记录")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除目录记录时发生错误: {e}")
            return False

    def _delete_movies_in_directory(self, directory_path: str) -> int:
        """
        删除指定目录下的所有影片及其关联数据

        注意：此操作只删除数据库中的记录，不会删除磁盘上的实际文件

        Args:
            directory_path: 目录路径

        Returns:
            删除的影片数量
        """
        try:
            from app.models.models import Movie, movie_tags, movie_genres, movie_actors, VImage

            # 查找目录下的所有影片
            movies = self.db.query(Movie).filter(Movie.file_path.like(f"{directory_path}%")).all()
            movie_count = len(movies)

            if movie_count == 0:
                return 0

            movie_ids = [movie.id for movie in movies]

            # 删除关联数据
            # 删除影片-标签关联
            self.db.execute(movie_tags.delete().where(movie_tags.c.movie_id.in_(movie_ids)))

            # 删除影片-分类关联
            self.db.execute(movie_genres.delete().where(movie_genres.c.movie_id.in_(movie_ids)))

            # 删除影片-演员关联
            self.db.execute(movie_actors.delete().where(movie_actors.c.movie_id.in_(movie_ids)))

            # 删除相关的虚拟图片记录
            for movie in movies:
                self._cleanup_movie_images(movie)

            # 删除影片记录
            self.db.query(Movie).filter(Movie.id.in_(movie_ids)).delete(synchronize_session=False)

            logger.info(f"成功删除目录 {directory_path} 下的 {movie_count} 部影片及其关联数据")
            return movie_count

        except Exception as e:
            logger.error(f"删除目录下影片时发生错误: {e}")
            raise
    
    def update_scan_time(self, directory_id: int) -> bool:
        """
        更新目录的最后扫描时间

        Args:
            directory_id: 目录 ID

        Returns:
            是否更新成功
        """
        try:
            directory = self.get_directory_by_id(directory_id)
            if not directory:
                return False

            directory.last_scan_time = datetime.utcnow()
            self.db.commit()

            logger.info(f"更新目录扫描时间: {directory_id}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新扫描时间时发生错误: {e}")
            return False

    def _cleanup_movie_images(self, movie):
        """
        清理影片相关的虚拟图片记录

        Args:
            movie: 影片对象
        """
        try:
            from app.models.models import VImage

            # 收集所有图片UUID
            image_uuids = []
            if movie.poster_path:
                image_uuids.append(movie.poster_path)
            if movie.fanart_path:
                image_uuids.append(movie.fanart_path)
            if movie.thumb_path:
                image_uuids.append(movie.thumb_path)

            # 删除对应的虚拟映射记录
            for image_uuid in image_uuids:
                v_image = self.db.query(VImage).filter(VImage.uuid == image_uuid).first()
                if v_image:
                    self.db.delete(v_image)
                    logger.info(f"删除图片虚拟映射: {image_uuid}")

        except Exception as e:
            logger.error(f"清理影片图片时发生错误: {e}")
    
    def get_directory_stats(self) -> Dict[str, Any]:
        """
        获取目录统计信息
        
        Returns:
            统计信息字典
        """
        try:
            total_directories = self.db.query(Directory).count()
            enabled_directories = self.db.query(Directory).filter(Directory.enabled == True).count()
            
            return {
                "total_directories": total_directories,
                "enabled_directories": enabled_directories,
                "disabled_directories": total_directories - enabled_directories
            }
        except Exception as e:
            logger.error(f"获取目录统计信息时发生错误: {e}")
            return {
                "total_directories": 0,
                "enabled_directories": 0,
                "disabled_directories": 0
            }

    def generate_directory_cover(self, directory_id: int) -> bool:
        """
        为指定目录生成封面图片

        Args:
            directory_id: 目录ID

        Returns:
            是否成功生成封面
        """
        try:
            from app.models.models import Movie, VImage
            from app.services.image_service import ImageService
            import random

            # 获取目录信息
            directory = self.db.query(Directory).filter(Directory.id == directory_id).first()
            if not directory:
                logger.error(f"目录不存在: {directory_id}")
                return False

            # 查找该目录下有海报的影片（通过VImage表获取实际文件路径）
            # 构建所有路径的查询条件
            path_conditions = []
            for directory_path in directory.paths:
                path_conditions.append(Movie.file_path.like(f"{directory_path.path}%"))

            if not path_conditions:
                logger.warning(f"目录 {directory_id} 没有配置路径")
                return False

            movies_with_posters = self.db.query(Movie, VImage).join(
                VImage, Movie.poster_path == VImage.uuid
            ).filter(
                or_(*path_conditions),
                Movie.poster_path.isnot(None),
                Movie.poster_path != "",
                VImage.image_type == "poster"
            ).all()

            if not movies_with_posters:
                # 生成默认封面
                image_service = ImageService()
                default_cover = image_service.create_directory_cover([])

                if default_cover:
                    directory.cover_image_base64 = default_cover
                    directory.cover_generated_at = datetime.now()

                    try:
                        self.db.commit()
                        logger.info(f"默认封面已保存到数据库")
                        return True
                    except Exception as commit_error:
                        logger.error(f"保存默认封面到数据库失败: {commit_error}")
                        self.db.rollback()
                        return False
                return False

            # 随机选择最多4部影片的海报
            selected_movies = random.sample(
                movies_with_posters,
                min(4, len(movies_with_posters))
            )

            # 获取海报文件路径
            poster_paths = []
            for movie, vimage in selected_movies:
                if vimage.real_file_path and Path(vimage.real_file_path).exists():
                    poster_paths.append(vimage.real_file_path)
                else:
                    logger.warning(f"海报文件不存在: {vimage.real_file_path}")

            if not poster_paths:
                logger.warning(f"目录 {directory.name} 的海报文件都不存在")
                return False

            # 生成拼接封面
            image_service = ImageService()
            cover_base64 = image_service.create_directory_cover(poster_paths)

            if cover_base64:
                directory.cover_image_base64 = cover_base64
                directory.cover_generated_at = datetime.now()

                try:
                    self.db.commit()
                    logger.info(f"成功为目录 {directory.name} 生成并保存封面到数据库")
                    return True
                except Exception as commit_error:
                    logger.error(f"保存封面到数据库失败: {commit_error}")
                    self.db.rollback()
                    return False
            else:
                logger.error(f"为目录 {directory.name} 生成封面失败")
                return False

        except Exception as e:
            logger.error(f"生成目录封面时发生错误: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            self.db.rollback()
            return False

    def regenerate_all_covers(self) -> Dict[str, int]:
        """
        重新生成所有启用目录的封面

        Returns:
            生成结果统计
        """
        try:
            enabled_directories = self.get_all_directories(enabled_only=True)

            success_count = 0
            failed_count = 0

            for directory in enabled_directories:
                if self.generate_directory_cover(directory.id):
                    success_count += 1
                else:
                    failed_count += 1

            logger.info(f"封面重新生成完成: 成功 {success_count}, 失败 {failed_count}")

            return {
                "success_count": success_count,
                "failed_count": failed_count,
                "total_count": len(enabled_directories)
            }

        except Exception as e:
            logger.error(f"重新生成所有封面时发生错误: {e}")
            return {
                "success_count": 0,
                "failed_count": 0,
                "total_count": 0
            }

    def get_directory_cover_base64(self, directory_id: int) -> Optional[str]:
        """
        获取目录的封面图片base64编码

        Args:
            directory_id: 目录ID

        Returns:
            base64编码的封面图片，如果没有则返回None
        """
        try:
            directory = self.db.query(Directory).filter(Directory.id == directory_id).first()
            if directory and directory.cover_image_base64:
                return directory.cover_image_base64
            return None

        except Exception as e:
            logger.error(f"获取目录封面时发生错误: {e}")
            return None







    def get_primary_path(self, directory_id: int) -> Optional[DirectoryPath]:
        """
        获取目录的主路径

        Args:
            directory_id: 目录ID

        Returns:
            主路径对象，如果没有则返回第一个路径
        """
        try:
            # 首先尝试获取标记为主路径的路径
            primary_path = self.db.query(DirectoryPath).filter(
                DirectoryPath.directory_id == directory_id,
                DirectoryPath.is_primary == True
            ).first()

            if primary_path:
                return primary_path

            # 如果没有主路径，返回第一个路径
            return self.db.query(DirectoryPath).filter(
                DirectoryPath.directory_id == directory_id
            ).order_by(DirectoryPath.created_at.asc()).first()

        except Exception as e:
            logger.error(f"获取主路径时发生错误: {e}")
            return None



    def validate_path(self, path: str) -> Dict[str, Any]:
        """
        验证路径的有效性

        Args:
            path: 路径字符串

        Returns:
            验证结果字典，包含is_valid, exists, normalized_path等信息
        """
        try:
            # 规范化路径
            normalized_path = os.path.normpath(path)

            # 检查路径格式
            is_valid_format = bool(normalized_path and normalized_path.strip())

            # 检查路径是否存在
            path_exists = Path(normalized_path).exists() if is_valid_format else False

            # 检查是否是目录
            is_directory = Path(normalized_path).is_dir() if path_exists else False

            # 检查路径是否已被使用
            existing_path = self.db.query(DirectoryPath).filter(DirectoryPath.path == normalized_path).first()
            is_duplicate = existing_path is not None

            return {
                'is_valid': is_valid_format and path_exists and is_directory and not is_duplicate,
                'normalized_path': normalized_path,
                'exists': path_exists,
                'is_directory': is_directory,
                'is_duplicate': is_duplicate,
                'existing_directory_name': existing_path.directory.name if existing_path else None
            }

        except Exception as e:
            logger.error(f"验证路径时发生错误: {e}")
            return {
                'is_valid': False,
                'normalized_path': path,
                'exists': False,
                'is_directory': False,
                'is_duplicate': False,
                'error': str(e)
            }

    def create_directory_with_paths(self, name: str, enabled: bool = True, paths: List[Dict[str, Any]] = None) -> Optional[Directory]:
        """
        创建目录并设置多个路径

        Args:
            name: 目录名称
            enabled: 是否启用
            paths: 路径列表，每个路径包含 path 和 enabled 字段

        Returns:
            创建的目录对象，如果失败则返回 None

        Raises:
            ValueError: 当名称重复或路径无效时
        """
        try:
            # 检查名称是否已存在
            existing_name = self.db.query(Directory).filter(Directory.name == name).first()
            if existing_name:
                raise ValueError(f"目录名称 '{name}' 已存在")

            if not paths:
                raise ValueError("至少需要一个路径")

            # 验证所有路径
            for path_data in paths:
                path = path_data.get('path', '').strip()
                if not path:
                    raise ValueError("路径不能为空")

                # 检查路径是否已存在
                existing_path = self.db.query(DirectoryPath).filter(DirectoryPath.path == path).first()
                if existing_path:
                    raise ValueError(f"路径 '{path}' 已被其他目录使用")

            # 创建目录
            directory = Directory(
                name=name,
                enabled=enabled
            )

            self.db.add(directory)
            self.db.flush()  # 获取目录ID

            # 创建路径记录
            for i, path_data in enumerate(paths):
                path = path_data.get('path', '').strip()
                path_enabled = path_data.get('enabled', True)
                is_primary = (i == 0)  # 第一个路径为主路径

                directory_path = DirectoryPath(
                    directory_id=directory.id,
                    path=path,
                    is_primary=is_primary,
                    enabled=path_enabled
                )
                self.db.add(directory_path)

            self.db.commit()
            self.db.refresh(directory)

            logger.info(f"成功创建目录 '{name}' 及其 {len(paths)} 个路径")
            return directory

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建目录及路径时发生错误: {e}")
            raise

    def update_directory_with_paths(self, directory_id: int, name: str = None, enabled: bool = None, paths: List[Dict[str, Any]] = None) -> Optional[Directory]:
        """
        更新目录及其路径

        Args:
            directory_id: 目录ID
            name: 新的目录名称
            enabled: 是否启用
            paths: 路径列表，每个路径包含 id（可选）、path 和 enabled 字段

        Returns:
            更新后的目录对象，如果失败则返回 None

        Raises:
            ValueError: 当名称重复或路径无效时
        """
        try:
            # 获取现有目录
            directory = self.db.query(Directory).filter(Directory.id == directory_id).first()
            if not directory:
                raise ValueError(f"目录 ID {directory_id} 不存在")

            # 检查名称是否重复（排除当前目录）
            if name and name != directory.name:
                existing_name = self.db.query(Directory).filter(
                    Directory.name == name,
                    Directory.id != directory_id
                ).first()
                if existing_name:
                    raise ValueError(f"目录名称 '{name}' 已存在")

            # 更新目录基本信息
            if name is not None:
                directory.name = name
            if enabled is not None:
                directory.enabled = enabled

            # 处理路径更新
            if paths is not None:
                if not paths:
                    raise ValueError("至少需要一个路径")

                # 验证所有路径（排除当前目录的现有路径）
                for path_data in paths:
                    path = path_data.get('path', '').strip()
                    if not path:
                        raise ValueError("路径不能为空")

                    path_id = path_data.get('id')
                    # 检查路径是否已被其他目录使用
                    existing_path_query = self.db.query(DirectoryPath).filter(DirectoryPath.path == path)
                    if path_id:
                        # 如果是更新现有路径，排除自己
                        existing_path_query = existing_path_query.filter(DirectoryPath.id != path_id)
                    else:
                        # 如果是新路径，排除当前目录的所有路径
                        existing_path_query = existing_path_query.filter(DirectoryPath.directory_id != directory_id)

                    existing_path = existing_path_query.first()
                    if existing_path:
                        raise ValueError(f"路径 '{path}' 已被其他目录使用")

                # 获取当前目录的所有路径
                current_paths = self.db.query(DirectoryPath).filter(DirectoryPath.directory_id == directory_id).all()
                current_path_ids = {p.id for p in current_paths}

                # 处理路径更新
                updated_path_ids = set()
                primary_path_set = False

                for i, path_data in enumerate(paths):
                    path = path_data.get('path', '').strip()
                    path_enabled = path_data.get('enabled', True)
                    path_id = path_data.get('id')
                    is_primary = (i == 0)  # 第一个路径为主路径

                    if path_id and path_id in current_path_ids:
                        # 更新现有路径
                        existing_path = self.db.query(DirectoryPath).filter(DirectoryPath.id == path_id).first()
                        if existing_path:
                            existing_path.path = path
                            existing_path.enabled = path_enabled
                            existing_path.is_primary = is_primary
                            updated_path_ids.add(path_id)
                    else:
                        # 创建新路径
                        new_path = DirectoryPath(
                            directory_id=directory_id,
                            path=path,
                            is_primary=is_primary,
                            enabled=path_enabled
                        )
                        self.db.add(new_path)



                # 删除不再需要的路径
                paths_to_delete = current_path_ids - updated_path_ids
                if paths_to_delete:
                    self.db.query(DirectoryPath).filter(
                        DirectoryPath.id.in_(paths_to_delete)
                    ).delete(synchronize_session=False)

            self.db.commit()
            self.db.refresh(directory)

            logger.info(f"成功更新目录 '{directory.name}' (ID: {directory_id})")
            return directory

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新目录及路径时发生错误: {e}")
            raise


