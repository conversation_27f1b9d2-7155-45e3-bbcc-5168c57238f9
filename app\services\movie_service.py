"""
影片管理服务
提供影片的查询、更新、删除等业务逻辑
"""
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from app.models.models import Movie, Tag, Genre, Actor, Series, VImage, Directory, Favorite
from app.schemas.schemas import (
    MovieUpdate, MovieFilterParams, BatchAssociationRequest,
    MovieDeleteRequest, BatchDeleteResult, BatchDeleteFailedItem
)
import logging

logger = logging.getLogger(__name__)


class MovieService:
    """影片管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_movies(self, params: MovieFilterParams) -> Tuple[List[Movie], int]:
        """
        获取影片列表
        
        Args:
            params: 过滤参数
            
        Returns:
            (影片列表, 总数量)
        """
        try:
            from app.models.models import Directory

            query = self.db.query(Movie)

            # 只显示启用目录中的影片
            query = query.join(Directory).filter(Directory.enabled == True)

            # 应用搜索过滤
            if params.search:
                search_term = f"%{params.search}%"
                query = query.filter(
                    or_(
                        Movie.title.ilike(search_term),
                        Movie.original_title.ilike(search_term),
                        Movie.plot.ilike(search_term),
                        # 搜索演员名称
                        Movie.actors.any(Actor.name.ilike(search_term))
                    )
                )
            
            # 应用分类过滤（多选）
            if params.genre_ids:
                query = query.filter(Movie.genres.any(Genre.id.in_(params.genre_ids)))

            # 应用系列过滤（多选）
            if params.series_ids:
                query = query.filter(Movie.series_id.in_(params.series_ids))

            # 应用标签过滤（多选）
            if params.tag_ids:
                query = query.filter(Movie.tags.any(Tag.id.in_(params.tag_ids)))

            # 应用年份过滤
            if params.year:
                query = query.filter(Movie.year == params.year)
            elif params.year_from or params.year_to:
                if params.year_from:
                    query = query.filter(Movie.year >= params.year_from)
                if params.year_to:
                    query = query.filter(Movie.year <= params.year_to)

            # 应用评分过滤
            if params.rating_min:
                query = query.filter(Movie.rating >= params.rating_min)
            elif params.rating_from or params.rating_to:
                if params.rating_from:
                    query = query.filter(Movie.rating >= params.rating_from)
                if params.rating_to:
                    query = query.filter(Movie.rating <= params.rating_to)

            # 应用目录ID过滤
            if params.directory_id:
                query = query.filter(Movie.directory_id == params.directory_id)
            
            # 获取总数
            total_count = query.count()

            # 应用排序
            sort_column = Movie.title  # 默认按标题排序
            if params.sort_by == 'year':
                sort_column = Movie.year
            elif params.sort_by == 'rating':
                sort_column = Movie.rating
            elif params.sort_by == 'created_at':
                sort_column = Movie.created_at
            elif params.sort_by == 'title':
                sort_column = Movie.title

            # 应用排序方向
            if params.sort_order == 'desc':
                sort_column = sort_column.desc()
            else:
                sort_column = sort_column.asc()

            # 应用分页和排序
            movies = query.order_by(sort_column).offset(params.offset).limit(params.limit).all()
            
            logger.info(f"获取影片列表: {len(movies)} 条记录，总计 {total_count} 条")
            return movies, total_count
            
        except Exception as e:
            logger.error(f"获取影片列表时发生错误: {e}")
            return [], 0
    
    def get_movie_by_id(self, movie_id: int, check_directory_enabled: bool = True) -> Optional[Movie]:
        """
        根据ID获取影片详情

        Args:
            movie_id: 影片ID
            check_directory_enabled: 是否检查目录启用状态

        Returns:
            影片对象或None
        """
        try:
            query = self.db.query(Movie).options(
                joinedload(Movie.series),
                joinedload(Movie.tags),
                joinedload(Movie.genres),
                joinedload(Movie.actors),
                joinedload(Movie.directory)
            ).filter(Movie.id == movie_id)

            if check_directory_enabled:
                # 只查询启用目录中的影片
                query = query.join(Directory).filter(Directory.enabled == True)

            movie = query.first()

            if movie:
                logger.info(f"获取影片详情: {movie.title} (ID: {movie_id})")
            else:
                logger.warning(f"影片不存在: ID {movie_id}")

            return movie

        except Exception as e:
            logger.error(f"获取影片详情时发生错误: {e}")
            return None




    def update_movie(self, movie_id: int, update_data: MovieUpdate) -> Optional[Movie]:
        """
        更新影片信息
        
        Args:
            movie_id: 影片ID
            update_data: 更新数据
            
        Returns:
            更新后的影片对象或None
        """
        try:
            movie = self.get_movie_by_id(movie_id)
            if not movie:
                return None
            
            # 更新基本信息
            update_dict = update_data.dict(exclude_unset=True, exclude={'tag_ids', 'genre_ids', 'actor_ids'})
            for field, value in update_dict.items():
                if hasattr(movie, field):
                    setattr(movie, field, value)
            
            # 更新关联关系
            if update_data.tag_ids is not None:
                self._update_movie_tags(movie, update_data.tag_ids)
            
            if update_data.genre_ids is not None:
                self._update_movie_genres(movie, update_data.genre_ids)
            
            if update_data.actor_ids is not None:
                self._update_movie_actors(movie, update_data.actor_ids)
            
            self.db.commit()
            logger.info(f"成功更新影片: {movie.title} (ID: {movie_id})")
            return movie
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新影片时发生错误: {e}")
            return None
    
    def delete_movie(self, movie_id: int, force: bool = False) -> Tuple[bool, str]:
        """
        删除影片记录
        
        Args:
            movie_id: 影片ID
            force: 是否强制删除
            
        Returns:
            (是否成功, 消息)
        """
        try:
            movie = self.get_movie_by_id(movie_id)
            if not movie:
                return False, "影片不存在"
            
            movie_title = movie.title
            
            # 清理关联的虚拟图片记录
            self._cleanup_movie_images(movie)
            
            # 删除影片记录（关联关系会自动清理）
            self.db.delete(movie)
            self.db.commit()
            
            logger.info(f"成功删除影片: {movie_title} (ID: {movie_id})")
            return True, f"成功删除影片: {movie_title}"
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除影片时发生错误: {e}")
            return False, f"删除影片失败: {str(e)}"

    def batch_delete_movies(self, request: MovieDeleteRequest) -> BatchDeleteResult:
        """
        批量删除影片记录

        Args:
            request: 批量删除请求

        Returns:
            批量删除结果
        """
        success_ids = []
        failed_items = []

        for movie_id in request.movies_ids:
            try:
                success, message = self.delete_movie(movie_id, force=request.force)
                if success:
                    success_ids.append(movie_id)
                else:
                    failed_items.append(BatchDeleteFailedItem(id=movie_id, error=message))
            except Exception as e:
                failed_items.append(BatchDeleteFailedItem(id=movie_id, error=f"删除失败: {str(e)}"))

        return BatchDeleteResult(
            total_count=len(request.movies_ids),
            success_count=len(success_ids),
            failed_count=len(failed_items),
            success_ids=success_ids,
            failed_items=failed_items
        )

    def get_movie_with_image_uuids(self, movie: Movie) -> Dict[str, Any]:
        """
        获取影片信息并包含图片UUID

        Args:
            movie: 影片对象

        Returns:
            包含图片UUID的影片信息字典
        """
        try:
            # 影片表中的路径字段实际上已经存储的是虚拟图片的UUID
            # 我们需要验证这些UUID是否在v_images表中存在
            poster_uuid = None
            fanart_uuid = None
            thumb_uuid = None

            # 收集所有可能的UUID
            potential_uuids = []
            uuid_to_field = {}

            if movie.poster_path:
                potential_uuids.append(movie.poster_path)
                uuid_to_field[movie.poster_path] = 'poster'

            if movie.fanart_path:
                potential_uuids.append(movie.fanart_path)
                uuid_to_field[movie.fanart_path] = 'fanart'

            if movie.thumb_path:
                potential_uuids.append(movie.thumb_path)
                uuid_to_field[movie.thumb_path] = 'thumb'

            # 验证这些UUID是否在v_images表中存在
            if potential_uuids:
                images = self.db.query(VImage).filter(
                    VImage.uuid.in_(potential_uuids)
                ).all()

                # 构建UUID到图片类型的映射
                valid_uuids = {}
                for image in images:
                    valid_uuids[image.uuid] = image.image_type

                # 分配有效的UUID
                for uuid, expected_type in uuid_to_field.items():
                    if uuid in valid_uuids:
                        # 验证图片类型是否匹配
                        actual_type = valid_uuids[uuid]
                        if expected_type == 'poster' and actual_type == 'poster':
                            poster_uuid = uuid
                        elif expected_type == 'fanart' and actual_type == 'fanart':
                            fanart_uuid = uuid
                        elif expected_type == 'thumb' and actual_type == 'thumb':
                            thumb_uuid = uuid
                        else:
                            # 如果类型不匹配，仍然使用UUID但记录警告
                            logger.warning(f"图片类型不匹配: UUID {uuid} 期望 {expected_type} 实际 {actual_type}")
                            if expected_type == 'poster':
                                poster_uuid = uuid
                            elif expected_type == 'fanart':
                                fanart_uuid = uuid
                            elif expected_type == 'thumb':
                                thumb_uuid = uuid

            return {
                "movie": movie,
                "poster_uuid": poster_uuid,
                "fanart_uuid": fanart_uuid,
                "thumb_uuid": thumb_uuid
            }

        except Exception as e:
            logger.error(f"获取影片图片UUID时发生错误: {e}")
            return {
                "movie": movie,
                "poster_uuid": None,
                "fanart_uuid": None,
                "thumb_uuid": None
            }

    def get_movies_with_image_uuids(self, movies: List[Movie]) -> Dict[int, Dict[str, str]]:
        """
        批量获取多个影片的图片UUID，提高性能

        Args:
            movies: 影片对象列表

        Returns:
            影片ID到图片UUID字典的映射
        """
        try:
            # 收集所有可能的UUID
            all_potential_uuids = set()
            movie_uuids = {}  # movie_id -> {poster_uuid, fanart_uuid, thumb_uuid}

            for movie in movies:
                uuids = {}
                if movie.poster_path:
                    all_potential_uuids.add(movie.poster_path)
                    uuids['poster'] = movie.poster_path
                if movie.fanart_path:
                    all_potential_uuids.add(movie.fanart_path)
                    uuids['fanart'] = movie.fanart_path
                if movie.thumb_path:
                    all_potential_uuids.add(movie.thumb_path)
                    uuids['thumb'] = movie.thumb_path
                movie_uuids[movie.id] = uuids

            # 批量验证UUID是否存在于v_images表中
            valid_uuids = set()
            uuid_to_type = {}
            if all_potential_uuids:
                images = self.db.query(VImage).filter(
                    VImage.uuid.in_(list(all_potential_uuids))
                ).all()

                for image in images:
                    valid_uuids.add(image.uuid)
                    uuid_to_type[image.uuid] = image.image_type

            # 构建结果
            result = {}
            for movie in movies:
                uuids = movie_uuids[movie.id]

                # 验证并分配UUID
                poster_uuid = None
                fanart_uuid = None
                thumb_uuid = None

                if 'poster' in uuids and uuids['poster'] in valid_uuids:
                    poster_uuid = uuids['poster']

                if 'fanart' in uuids and uuids['fanart'] in valid_uuids:
                    fanart_uuid = uuids['fanart']

                if 'thumb' in uuids and uuids['thumb'] in valid_uuids:
                    thumb_uuid = uuids['thumb']

                result[movie.id] = {
                    "poster_uuid": poster_uuid,
                    "fanart_uuid": fanart_uuid,
                    "thumb_uuid": thumb_uuid
                }

            return result

        except Exception as e:
            logger.error(f"批量获取影片图片UUID时发生错误: {e}")
            return {}

    def get_movies_favorite_status(self, movie_ids: List[int]) -> Dict[int, bool]:
        """
        批量获取影片的收藏状态

        Args:
            movie_ids: 影片ID列表

        Returns:
            影片ID到收藏状态的映射字典
        """
        try:
            if not movie_ids:
                return {}

            # 查询所有收藏的影片ID
            favorited_movie_ids = self.db.query(Favorite.movie_id).filter(
                Favorite.movie_id.in_(movie_ids)
            ).all()

            # 转换为集合以提高查找效率
            favorited_ids_set = {fav.movie_id for fav in favorited_movie_ids}

            # 构建结果字典
            result = {}
            for movie_id in movie_ids:
                result[movie_id] = movie_id in favorited_ids_set

            return result

        except Exception as e:
            logger.error(f"获取影片收藏状态时发生错误: {e}")
            return {movie_id: False for movie_id in movie_ids}


    def _update_movie_tags(self, movie: Movie, tag_ids: List[int]):
        """更新影片标签关联"""
        movie.tags.clear()
        if tag_ids:
            tags = self.db.query(Tag).filter(Tag.id.in_(tag_ids)).all()
            movie.tags.extend(tags)
    
    def _update_movie_genres(self, movie: Movie, genre_ids: List[int]):
        """更新影片分类关联"""
        movie.genres.clear()
        if genre_ids:
            genres = self.db.query(Genre).filter(Genre.id.in_(genre_ids)).all()
            movie.genres.extend(genres)
    
    def _update_movie_actors(self, movie: Movie, actor_ids: List[int]):
        """更新影片演员关联"""
        movie.actors.clear()
        if actor_ids:
            actors = self.db.query(Actor).filter(Actor.id.in_(actor_ids)).all()
            movie.actors.extend(actors)
    
    def _cleanup_movie_images(self, movie: Movie):
        """清理影片相关的虚拟图片记录"""
        try:
            image_paths = [movie.poster_path, movie.fanart_path, movie.thumb_path]
            for path in image_paths:
                if path:
                    self.db.query(VImage).filter(VImage.real_file_path == path).delete()
        except Exception as e:
            logger.error(f"清理影片图片时发生错误: {e}")

    def batch_add_associations(self, request: BatchAssociationRequest):
        """批量为影片添加关联"""
        try:
            movies = self.db.query(Movie).filter(Movie.id.in_(request.movie_ids)).all()
            if not movies:
                return

            if request.tag_ids:
                tags_to_add = self.db.query(Tag).filter(Tag.id.in_(request.tag_ids)).all()
                for movie in movies:
                    for tag in tags_to_add:
                        if tag not in movie.tags:
                            movie.tags.append(tag)

            if request.genre_ids:
                genres_to_add = self.db.query(Genre).filter(Genre.id.in_(request.genre_ids)).all()
                for movie in movies:
                    for genre in genres_to_add:
                        if genre not in movie.genres:
                            movie.genres.append(genre)

            if request.series_id:
                for movie in movies:
                    movie.series_id = request.series_id

            self.db.commit()
            logger.info(f"成功为 {len(movies)} 部影片批量添加关联")
        except Exception as e:
            self.db.rollback()
            logger.error(f"批量添加关联时发生错误: {e}")
            raise

    def batch_remove_associations(self, request: BatchAssociationRequest):
        """批量为影片移除关联"""
        try:
            movies = self.db.query(Movie).filter(Movie.id.in_(request.movie_ids)).all()
            if not movies:
                return

            if request.tag_ids:
                tags_to_remove = self.db.query(Tag).filter(Tag.id.in_(request.tag_ids)).all()
                for movie in movies:
                    for tag in tags_to_remove:
                        if tag in movie.tags:
                            movie.tags.remove(tag)

            if request.genre_ids:
                genres_to_remove = self.db.query(Genre).filter(Genre.id.in_(request.genre_ids)).all()
                for movie in movies:
                    for genre in genres_to_remove:
                        if genre in movie.genres:
                            movie.genres.remove(genre)

            if request.series_id:
                for movie in movies:
                    if movie.series_id == request.series_id:
                        movie.series_id = None

            self.db.commit()
            logger.info(f"成功为 {len(movies)} 部影片批量移除关联")
        except Exception as e:
            self.db.rollback()
            logger.error(f"批量移除关联时发生错误: {e}")
            raise
