"""
异步扫描服务
提供非阻塞的目录扫描功能，支持进度追踪和控制
"""
import asyncio
import threading
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path
import os
import logging

from app.services.scan_progress_service import scan_progress_service, ScanStatus
from app.services.media_scanner import MediaScanner
from app.services.nfo_parser import NFOParser
from app.services.database_service import DatabaseService
from app.services.directory_service import DirectoryService
from app.services.mapping_service import MappingService

logger = logging.getLogger(__name__)


class AsyncScanService:
    """异步扫描服务"""
    
    def __init__(self):
        self.executor = None
        
    async def start_directory_scan(
        self,
        db_session,
        directories: List[Any],
        incremental: bool = True,
        scan_type: str = "增量扫描"
    ) -> str:
        """
        开始异步目录扫描
        
        Args:
            db_session: 数据库会话
            directories: 要扫描的目录列表
            incremental: 是否增量扫描
            scan_type: 扫描类型描述
            
        Returns:
            任务ID
        """
        # 生成唯一任务ID
        task_id = str(uuid.uuid4())
        
        # 准备目录信息
        directory_info = []
        for directory in directories:
            directory_info.append({
                "id": directory.id,
                "name": directory.name,
                "paths": [{"path": path.path, "enabled": path.enabled} for path in directory.paths]
            })
        
        # 创建扫描任务
        scan_progress_service.create_scan_task(task_id, directory_info, scan_type)
        
        # 在后台线程中执行扫描
        threading.Thread(
            target=self._run_scan_in_thread,
            args=(task_id, db_session, directories, incremental),
            daemon=True
        ).start()
        
        return task_id
    
    def _run_scan_in_thread(
        self,
        task_id: str,
        db_session,
        directories: List[Any],
        incremental: bool
    ):
        """
        在后台线程中运行扫描
        
        Args:
            task_id: 任务ID
            db_session: 数据库会话
            directories: 目录列表
            incremental: 是否增量扫描
        """
        try:
            # 开始任务
            scan_progress_service.start_scan_task(task_id)

            # 初始化服务（共享实例以避免重复缓存加载）
            mapping_service = MappingService(db_session)
            mapping_service.refresh_cache()

            dir_service = DirectoryService(db_session)
            db_service = DatabaseService(db_session, mapping_service)
            media_scanner = MediaScanner(db_session)  # 共享 MediaScanner 实例

            # 统计信息
            total_new_movies = 0
            total_updated_movies = 0
            total_deleted_movies = 0
            total_processed_files = 0

            # 预扫描以获取总文件数
            total_files = self._prescan_directories(task_id, directories, media_scanner)
            scan_progress_service.update_scan_progress(
                task_id,
                total_files=total_files,
                processed_files=0  # 确保初始化为0
            )
            
            # 扫描每个目录
            for dir_index, directory in enumerate(directories):
                # 检查是否需要暂停或取消
                action = scan_progress_service.check_pause_or_cancel(task_id)
                if action == 'cancel':
                    logger.info(f"扫描任务 {task_id} 被取消")
                    return
                elif action == 'pause':
                    logger.info(f"扫描任务 {task_id} 被暂停")
                    # 等待恢复或取消
                    while True:
                        action = scan_progress_service.check_pause_or_cancel(task_id)
                        if action == 'cancel':
                            logger.info(f"扫描任务 {task_id} 被取消")
                            return
                        elif action == 'continue':
                            logger.info(f"扫描任务 {task_id} 恢复运行")
                            break
                        threading.Event().wait(1)  # 等待1秒
                
                # 更新当前目录
                scan_progress_service.update_scan_progress(
                    task_id,
                    current_directory_index=dir_index,
                    current_directory_name=directory.name
                )
                
                # 扫描目录
                directory_result = self._scan_single_directory(
                    task_id, directory, incremental, dir_service, db_service, media_scanner
                )
                
                total_new_movies += directory_result.get("new_movies", 0)
                total_updated_movies += directory_result.get("updated_movies", 0)
                total_deleted_movies += directory_result.get("deleted_movies", 0)
                total_processed_files += directory_result.get("processed_files", 0)

            # 对于全量扫描，进行全局删除检测
            if not incremental:
                try:
                    logger.info("全量扫描：开始全局删除检测...")
                    global_cleanup_result = db_service.cleanup_all_deleted_movies()
                    global_deleted_count = global_cleanup_result.get("deleted_count", 0)

                    # 更新总删除数量（避免重复计算已在目录扫描中删除的影片）
                    additional_deleted = max(0, global_deleted_count - total_deleted_movies)
                    total_deleted_movies = global_deleted_count

                    if additional_deleted > 0:
                        logger.info(f"全局删除检测额外清理了 {additional_deleted} 个影片记录")

                except Exception as e:
                    logger.error(f"全局删除检测时发生错误: {e}")

            # 完成扫描
            scan_progress_service.update_scan_progress(
                task_id,
                new_movies=total_new_movies,
                updated_movies=total_updated_movies,
                deleted_movies=total_deleted_movies,
                processed_files=total_processed_files
            )
            
            scan_progress_service.complete_scan_task(task_id, success=True)
            logger.info(f"扫描任务 {task_id} 完成")
            
        except Exception as e:
            logger.error(f"扫描任务 {task_id} 发生错误: {e}")
            scan_progress_service.complete_scan_task(
                task_id, success=False, error_message=str(e)
            )
    
    def _prescan_directories(self, task_id: str, directories: List[Any], media_scanner: MediaScanner) -> int:
        """
        预扫描目录以获取总文件数

        Args:
            task_id: 任务ID
            directories: 目录列表
            media_scanner: 媒体扫描器实例

        Returns:
            总文件数
        """
        total_files = 0
        
        for directory in directories:
            # 检查是否需要取消
            action = scan_progress_service.check_pause_or_cancel(task_id)
            if action == 'cancel':
                break
            
            enabled_paths = [path for path in directory.paths if path.enabled]
            
            for path_obj in enabled_paths:
                try:
                    media_files = media_scanner.scan_directory(path_obj.path, recursive=True)
                    total_files += len(media_files)
                except Exception as e:
                    logger.error(f"预扫描路径 {path_obj.path} 时发生错误: {e}")
        
        return total_files
    
    def _scan_single_directory(
        self,
        task_id: str,
        directory: Any,
        incremental: bool,
        dir_service: DirectoryService,
        db_service: DatabaseService,
        media_scanner: MediaScanner
    ) -> Dict[str, Any]:
        """
        扫描单个目录

        Args:
            task_id: 任务ID
            directory: 目录对象
            incremental: 是否增量扫描
            dir_service: 目录服务
            db_service: 数据库服务
            media_scanner: 媒体扫描器实例

        Returns:
            扫描结果
        """
        result = {
            "new_movies": 0,
            "updated_movies": 0,
            "deleted_movies": 0,
            "processed_files": 0
        }
        
        try:
            # 获取启用的路径
            enabled_paths = [path for path in directory.paths if path.enabled]
            if not enabled_paths:
                logger.warning(f"目录 '{directory.name}' 没有启用的路径，跳过扫描")
                return result
            
            # 扫描所有路径
            all_media_files = []
            for path_obj in enabled_paths:
                try:
                    media_files = media_scanner.scan_directory(path_obj.path, recursive=True)
                    all_media_files.extend(media_files)
                except Exception as e:
                    logger.error(f"扫描路径 '{path_obj.path}' 时发生错误: {e}")
            
            # 增量扫描过滤
            if incremental and directory.last_scan_time:
                filtered_files = []
                for media_file in all_media_files:
                    file_path = Path(media_file['file_path'])
                    if file_path.exists():
                        file_mtime = os.path.getmtime(file_path)
                        if file_mtime > directory.last_scan_time.timestamp():
                            filtered_files.append(media_file)
                all_media_files = filtered_files
            
            # 处理每个媒体文件
            for file_index, media_file in enumerate(all_media_files):
                # 检查是否需要暂停或取消
                action = scan_progress_service.check_pause_or_cancel(task_id)
                if action == 'cancel':
                    break
                elif action == 'pause':
                    # 等待恢复
                    while True:
                        action = scan_progress_service.check_pause_or_cancel(task_id)
                        if action == 'cancel':
                            return result
                        elif action == 'continue':
                            break
                        threading.Event().wait(1)

                # 增加已处理文件计数
                result["processed_files"] += 1

                # 获取当前总处理文件数并更新
                current_progress = scan_progress_service.get_scan_progress(task_id)
                current_total_processed = current_progress.get("processed_files", 0) if current_progress else 0

                # 更新当前文件和进度
                scan_progress_service.update_scan_progress(
                    task_id,
                    current_file_path=media_file['file_path'],
                    processed_files=current_total_processed + 1
                )
                
                # 处理NFO文件
                if media_file['nfo_path']:
                    movie_data = NFOParser.parse_nfo_file(media_file['nfo_path'])
                    if movie_data:
                        movie_data['file_info'] = media_file
                        
                        # 检查是否已存在
                        existing_movie = db_service.get_movie_by_file_path(media_file['file_path'])
                        
                        if existing_movie:
                            # 更新现有影片
                            updated_movie = db_service.update_movie_data(
                                existing_movie.id, movie_data, media_file, directory.id
                            )
                            if updated_movie:
                                result["updated_movies"] += 1
                        else:
                            # 保存新影片
                            saved_movie = db_service.save_movie_data(
                                movie_data, media_file, directory.id
                            )
                            if saved_movie:
                                result["new_movies"] += 1

            # 检测并清理已删除的影片
            try:
                logger.info(f"开始检测目录 {directory.name} 中已删除的影片...")
                cleanup_result = db_service.cleanup_deleted_movies_by_directory(directory.id)
                result["deleted_movies"] = cleanup_result.get("deleted_count", 0)

                # 更新扫描进度中的删除统计
                scan_progress_service.update_scan_progress(
                    task_id,
                    deleted_movies=scan_progress_service.get_scan_progress(task_id)["deleted_movies"] + result["deleted_movies"]
                )

                if result["deleted_movies"] > 0:
                    logger.info(f"目录 {directory.name} 清理了 {result['deleted_movies']} 个已删除的影片记录")

            except Exception as e:
                logger.error(f"检测删除影片时发生错误: {e}")

            # 生成目录封面
            try:
                dir_service.generate_directory_cover(directory.id)
            except Exception as e:
                logger.error(f"生成目录封面时发生错误: {e}")

            # 更新扫描时间
            dir_service.update_scan_time(directory.id)
            
        except Exception as e:
            logger.error(f"扫描目录 {directory.name} 时发生错误: {e}")
        
        return result
    
    def get_scan_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取扫描进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            进度信息
        """
        return scan_progress_service.get_scan_progress(task_id)
    
    def pause_scan(self, task_id: str) -> bool:
        """
        暂停扫描
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        return scan_progress_service.pause_scan_task(task_id)
    
    def resume_scan(self, task_id: str) -> bool:
        """
        恢复扫描
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        return scan_progress_service.resume_scan_task(task_id)
    
    def cancel_scan(self, task_id: str) -> bool:
        """
        取消扫描
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        return scan_progress_service.cancel_scan_task(task_id)


# 全局异步扫描服务实例
async_scan_service = AsyncScanService()
