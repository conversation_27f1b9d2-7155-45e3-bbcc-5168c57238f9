/**
 * 电影编辑页面 JavaScript
 * 提供电影信息编辑的完整功能
 */

window.MovieEdit = (function() {
    'use strict';

    // 私有变量
    let currentMovie = null;
    let accessType = null;
    let movieId = null;
    let isLoading = false;
    let isDirty = false; // 表单是否有未保存的更改

    // DOM 元素缓存
    const elements = {
        loadingContainer: null,
        errorContainer: null,
        editFormContainer: null,
        form: null,
        backBtn: null,
        saveBtn: null,
        cancelBtn: null,
        pageTitle: null,
        errorMessage: null
    };

    // 选项数据缓存
    const optionsData = {
        series: [],
        genres: [],
        tags: [],
        actors: []
    };

    // 下拉框组件实例
    const selectComponents = {
        series: null,  // 单选下拉框
        genres: null,  // 多选下拉框
        tags: null,    // 多选下拉框
        actors: null   // 多选下拉框
    };

    /**
     * 初始化页面
     */
    function init(type, id) {
        accessType = type;
        movieId = id;

        // 缓存 DOM 元素
        cacheElements();
        
        // 绑定事件
        bindEvents();
        
        // 加载数据
        loadPageData();
    }

    /**
     * 缓存 DOM 元素
     */
    function cacheElements() {
        elements.loadingContainer = document.getElementById('loading-container');
        elements.errorContainer = document.getElementById('error-container');
        elements.editFormContainer = document.getElementById('edit-form-container');
        elements.form = document.getElementById('movie-edit-form');
        elements.backBtn = document.getElementById('back-btn');
        elements.saveBtn = document.getElementById('save-btn');
        elements.cancelBtn = document.getElementById('cancel-btn');
        elements.pageTitle = document.getElementById('page-title');
        elements.errorMessage = document.getElementById('error-message');
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        // 返回按钮
        if (elements.backBtn) {
            elements.backBtn.addEventListener('click', handleBackClick);
        }

        // 保存按钮
        if (elements.saveBtn) {
            elements.saveBtn.addEventListener('click', handleSaveClick);
        }

        // 取消按钮
        if (elements.cancelBtn) {
            elements.cancelBtn.addEventListener('click', handleCancelClick);
        }

        // 表单变化监听
        if (elements.form) {
            elements.form.addEventListener('input', handleFormChange);
            elements.form.addEventListener('change', handleFormChange);
        }

        // 页面离开前确认
        window.addEventListener('beforeunload', handleBeforeUnload);
    }

    /**
     * 加载页面数据
     */
    async function loadPageData() {
        showLoading();
        
        try {
            // 并行加载电影数据和选项数据
            const [movieData, optionsResult] = await Promise.all([
                loadMovieData(),
                loadOptionsData()
            ]);

            if (movieData) {
                currentMovie = movieData;
                populateForm(movieData);
                updatePageTitle(movieData.title);
                showEditForm();
            } else {
                throw new Error('无法加载电影数据');
            }
        } catch (error) {
            console.error('加载页面数据失败:', error);
            showError(error.message || '加载数据失败，请稍后重试');
        }
    }



    /**
     * 加载电影数据
     */
    async function loadMovieData() {
        try {
            if (accessType === 'id' && movieId) {
                const result = await api.getMovie(movieId);

                if (result.success && result.data) {
                    return result.data;
                } else {
                    throw new Error(result.message || '获取电影详情失败');
                }
            } else {
                throw new Error('无效的访问参数');
            }
        } catch (error) {
            console.error('加载电影数据失败:', error);
            throw error;
        }
    }

    /**
     * 加载选项数据
     */
    async function loadOptionsData() {
        try {
            const [seriesData, genresData, tagsData, actorsData] = await Promise.all([
                api.getSeries(),
                api.getGenres(),
                api.getTags(),
                api.getActors()
            ]);

            // 缓存选项数据 - 修正数据结构
            optionsData.series = seriesData.success ? seriesData.data : [];
            optionsData.genres = genresData.success ? genresData.data : [];
            optionsData.tags = tagsData.success ? tagsData.data : [];
            optionsData.actors = actorsData.success ? actorsData.data : [];

            // 渲染选项
            renderOptions();

            return true;
        } catch (error) {
            console.error('加载选项数据失败:', error);
            // 选项数据加载失败不应该阻止页面显示
            return false;
        }
    }

    /**
     * 渲染选项数据
     */
    function renderOptions() {
        // 初始化下拉框组件
        initSelectComponents();
    }



    /**
     * 初始化下拉框组件
     */
    function initSelectComponents() {
        // 初始化系列单选框
        const seriesContainer = document.getElementById('series-container');
        if (seriesContainer) {
            selectComponents.series = new SingleSelectDropdown(seriesContainer, {
                placeholder: '请选择系列...',
                searchPlaceholder: '搜索系列...',
                noDataText: '暂无系列数据',
                noResultsText: '未找到匹配的系列',
                allowClear: true,
                allowCreate: true,
                createText: '按回车创建新系列',
                createApiUrl: '/api/series',
                createApiMethod: 'POST',
                onCreateSuccess: (newItem) => {
                    // 更新系列数据
                    optionsData.series.push(newItem);
                },
                onCreateError: (error) => {
                    console.error('系列创建失败:', error);
                },
                onChange: (selectedItem) => {
                    // 标记表单为已修改
                    markFormDirty();
                }
            });
            selectComponents.series.setData(optionsData.series);
        }

        // 初始化分类多选框
        const genresContainer = document.getElementById('genres-container');
        if (genresContainer) {
            selectComponents.genres = new MultiSelectDropdown(genresContainer, {
                placeholder: '请选择分类...',
                searchPlaceholder: '搜索分类...',
                noDataText: '暂无分类数据',
                maxDisplayTags: 3,
                allowCreate: true,
                createText: '按回车创建分类',
                createApiUrl: '/api/genres',
                type: 'genres',
                onCreateSuccess: (newItem) => {
                    // 分类创建成功
                },
                onCreateError: (error) => {
                    console.error('分类创建失败:', error);
                }
            });
            selectComponents.genres.setData(optionsData.genres);
        }

        // 初始化标签多选框
        const tagsContainer = document.getElementById('tags-container');
        if (tagsContainer) {
            selectComponents.tags = new MultiSelectDropdown(tagsContainer, {
                placeholder: '请选择标签...',
                searchPlaceholder: '搜索标签...',
                noDataText: '暂无标签数据',
                maxDisplayTags: 3,
                allowCreate: true,
                createText: '按回车创建标签',
                createApiUrl: '/api/tags',
                type: 'tags',
                onCreateSuccess: (newItem) => {
                    // 标签创建成功
                },
                onCreateError: (error) => {
                    console.error('标签创建失败:', error);
                }
            });
            selectComponents.tags.setData(optionsData.tags);
        }

        // 初始化演员多选框
        const actorsContainer = document.getElementById('actors-container');
        if (actorsContainer) {
            selectComponents.actors = new MultiSelectDropdown(actorsContainer, {
                placeholder: '请选择演员...',
                searchPlaceholder: '搜索演员（可输入"姓名 (角色)"）...',
                noDataText: '暂无演员数据',
                maxDisplayTags: 2,
                allowCreate: true,
                createText: '按回车创建演员',
                createApiUrl: '/api/actors',
                type: 'actors',
                onCreateSuccess: (newItem) => {
                    // 演员创建成功
                },
                onCreateError: (error) => {
                    console.error('演员创建失败:', error);
                }
            });
            selectComponents.actors.setData(optionsData.actors);
        }
    }

    /**
     * 填充表单数据
     */
    function populateForm(movie) {
        // 基本信息字段
        const basicFields = [
            'title', 'original_title', 'year', 'rating', 'runtime', 
            'country', 'critic_rating', 'num', 'outline', 'plot', 'trailer'
        ];

        basicFields.forEach(field => {
            const element = document.getElementById(field);
            if (element && movie[field] !== null && movie[field] !== undefined) {
                element.value = movie[field];
            }
        });

        // 设置海报
        const posterImg = document.getElementById('movie-poster');
        const posterInfo = document.getElementById('poster-info');
        if (movie.poster_uuid) {
            posterImg.src = `/api/images/${movie.poster_uuid}`;
            posterImg.alt = movie.title;
            posterInfo.textContent = '已设置海报';
        } else {
            posterImg.src = '';
            posterInfo.textContent = '暂无海报';
        }

        // 设置文件路径
        const filePathElement = document.getElementById('movie-file-path');
        if (filePathElement) {
            filePathElement.textContent = movie.file_path || '-';
        }

        // 设置系列
        if (selectComponents.series && movie.series) {
            selectComponents.series.setValue(movie.series.id);
        }

        // 设置分类
        if (selectComponents.genres && movie.genres) {
            selectComponents.genres.setSelectedValues(movie.genres);
        }

        // 设置标签
        if (selectComponents.tags && movie.tags) {
            selectComponents.tags.setSelectedValues(movie.tags);
        }

        // 设置演员
        if (selectComponents.actors && movie.actors) {
            selectComponents.actors.setSelectedValues(movie.actors);
        }

        // 重置脏标记
        isDirty = false;
    }

    /**
     * 更新页面标题
     */
    function updatePageTitle(title) {
        if (elements.pageTitle) {
            elements.pageTitle.textContent = `编辑影片 - ${title}`;
        }
        document.title = `编辑 ${title} - 媒体管理器`;
    }

    /**
     * 显示加载状态
     */
    function showLoading() {
        elements.loadingContainer?.classList.remove('hidden');
        elements.errorContainer?.classList.add('hidden');
        elements.editFormContainer?.classList.add('hidden');
    }

    /**
     * 显示错误状态
     */
    function showError(message) {
        if (elements.errorMessage) {
            elements.errorMessage.textContent = message;
        }
        elements.loadingContainer?.classList.add('hidden');
        elements.errorContainer?.classList.remove('hidden');
        elements.editFormContainer?.classList.add('hidden');
    }

    /**
     * 显示编辑表单
     */
    function showEditForm() {
        elements.loadingContainer?.classList.add('hidden');
        elements.errorContainer?.classList.add('hidden');
        elements.editFormContainer?.classList.remove('hidden');
        elements.saveBtn?.classList.remove('hidden');
        elements.cancelBtn?.classList.remove('hidden');
    }

    /**
     * 处理返回按钮点击
     */
    function handleBackClick(e) {
        e.preventDefault();

        if (isDirty) {
            if (confirm('您有未保存的更改，确定要离开吗？')) {
                navigateBack();
            }
        } else {
            navigateBack();
        }
    }

    /**
     * 导航回详情页
     */
    function navigateBack() {
        if (accessType === 'id' && movieId) {
            window.location.href = `/movies/info/${movieId}`;
        } else {
            window.location.href = '/movies';
        }
    }

    /**
     * 处理保存按钮点击
     */
    async function handleSaveClick(e) {
        e.preventDefault();

        if (isLoading) return;

        // 验证表单
        if (!validateForm()) {
            return;
        }

        // 保存数据
        await saveMovie();
    }

    /**
     * 处理取消按钮点击
     */
    function handleCancelClick(e) {
        e.preventDefault();

        if (isDirty) {
            if (confirm('您有未保存的更改，确定要取消吗？')) {
                // 重新填充表单数据
                populateForm(currentMovie);
            }
        }
    }

    /**
     * 处理表单变化
     */
    function handleFormChange() {
        isDirty = true;
    }

    /**
     * 处理页面离开前确认
     */
    function handleBeforeUnload(e) {
        if (isDirty) {
            e.preventDefault();
            e.returnValue = '';
        }
    }

    /**
     * 验证表单
     */
    function validateForm() {
        let isValid = true;

        // 清除之前的错误提示
        clearValidationErrors();

        // 验证必填字段
        const title = document.getElementById('title').value.trim();
        if (!title) {
            showFieldError('title', '影片标题不能为空');
            isValid = false;
        }

        // 验证年份
        const year = document.getElementById('year').value;
        if (year && (year < 1900 || year > 2100)) {
            showFieldError('year', '年份必须在1900-2100之间');
            isValid = false;
        }

        // 验证评分
        const rating = document.getElementById('rating').value;
        if (rating && (rating < 0 || rating > 10)) {
            showFieldError('rating', '评分必须在0-10之间');
            isValid = false;
        }

        // 验证影评人评分
        const criticRating = document.getElementById('critic_rating').value;
        if (criticRating && (criticRating < 0 || criticRating > 10)) {
            showFieldError('critic_rating', '影评人评分必须在0-10之间');
            isValid = false;
        }

        // 验证时长
        const runtime = document.getElementById('runtime').value;
        if (runtime && runtime < 0) {
            showFieldError('runtime', '时长不能为负数');
            isValid = false;
        }

        // 验证预告片链接
        const trailer = document.getElementById('trailer').value;
        if (trailer && !isValidUrl(trailer)) {
            showFieldError('trailer', '请输入有效的URL');
            isValid = false;
        }

        return isValid;
    }

    /**
     * 清除验证错误提示
     */
    function clearValidationErrors() {
        const errorElements = document.querySelectorAll('.label-text-alt.text-error');
        errorElements.forEach(element => {
            element.classList.add('hidden');
            element.textContent = '';
        });
    }

    /**
     * 显示字段错误
     */
    function showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }
    }

    /**
     * 验证URL格式
     */
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    /**
     * 保存电影数据
     */
    async function saveMovie() {
        isLoading = true;
        elements.saveBtn.disabled = true;
        elements.saveBtn.innerHTML = `
            <span class="loading loading-spinner loading-sm mr-2"></span>
            保存中...
        `;

        try {
            // 收集表单数据
            const formData = collectFormData();

            // 发送更新请求
            const result = await api.updateMovie(currentMovie.id, formData);

            if (result.success) {
                // 更新成功
                currentMovie = result.data;
                isDirty = false;

                // 显示成功提示
                if (window.toast) {
                    window.toast.success('影片信息更新成功');
                }

                // 可选：跳转回详情页
                setTimeout(() => {
                    navigateBack();
                }, 1000);

            } else {
                throw new Error(result.message || '更新失败');
            }

        } catch (error) {
            console.error('保存电影数据失败:', error);

            // 显示错误提示
            if (window.toast) {
                window.toast.error(error.message || '保存失败，请稍后重试');
            }

        } finally {
            isLoading = false;
            elements.saveBtn.disabled = false;
            elements.saveBtn.innerHTML = `
                <i class="bi bi-check mr-2" aria-label="保存图标"></i>
                保存更改
            `;
        }
    }

    /**
     * 收集表单数据
     */
    function collectFormData() {
        const formData = {};

        // 基本信息字段
        const basicFields = [
            'title', 'original_title', 'year', 'rating', 'runtime',
            'country', 'critic_rating', 'num', 'outline', 'plot', 'trailer'
        ];

        basicFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                let value = element.value.trim();

                // 处理数字字段
                if (['year', 'rating', 'runtime', 'critic_rating'].includes(field)) {
                    value = value ? parseFloat(value) : null;
                }

                // 处理空字符串
                if (value === '') {
                    value = null;
                }

                formData[field] = value;
            }
        });

        // 系列
        formData.series_id = selectComponents.series ? selectComponents.series.getValue() : null;
        if (formData.series_id) {
            formData.series_id = parseInt(formData.series_id);
        }

        // 分类
        formData.genre_ids = selectComponents.genres ? selectComponents.genres.getSelectedValues() : [];

        // 标签
        formData.tag_ids = selectComponents.tags ? selectComponents.tags.getSelectedValues() : [];

        // 演员
        formData.actor_ids = selectComponents.actors ? selectComponents.actors.getSelectedValues() : [];

        return formData;
    }

    /**
     * 标记表单为已修改
     */
    function markFormDirty() {
        isDirty = true;
    }

    // 公共接口
    return {
        init: init
    };
})();
